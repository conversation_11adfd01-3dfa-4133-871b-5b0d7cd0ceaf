from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import logging

app = Flask(__name__)
CORS(app)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.route('/')
def index():
    """Serve the main voice calling interface."""
    return render_template('index.html')

@app.route('/start-call', methods=['POST'])
def start_call():
    """Handle start call request."""
    try:
        logger.info("Start call request received")
        return jsonify({
            'status': 'success',
            'message': 'Call start request processed'
        })
    except Exception as e:
        logger.error(f"Error starting call: {e}")
        return jsonify({
            'status': 'error',
            'message': 'Failed to start call'
        }), 500

@app.route('/end-call', methods=['POST'])
def end_call():
    """Handle end call request."""
    try:
        logger.info("End call request received")
        return jsonify({
            'status': 'success',
            'message': 'Call end request processed'
        })
    except Exception as e:
        logger.error(f"Error ending call: {e}")
        return jsonify({
            'status': 'error',
            'message': 'Failed to end call'
        }), 500

@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    print("Starting Voice Calling WebApp on port 1800...")
    app.run(host='0.0.0.0', port=1800, debug=True)