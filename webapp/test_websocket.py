#!/usr/bin/env python3
"""
Simple WebSocket client to test backend connection
"""
import asyncio
import json
import websockets

async def test_websocket():
    uri = "ws://localhost:5010"
    
    try:
        print(f"Connecting to {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected successfully!")
            
            # Test user registration
            test_message = {
                "type": "store_user",
                "session": "test_session",
                "data": {
                    "name": "Test User",
                    "mobile": "test",
                    "userId": "test_session",
                    "sessionType": "call",
                    "target": "english_tutor"
                }
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Sent user registration message")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                data = json.loads(response)
                print(f"📥 Received response: {data}")
                print("✅ Backend WebSocket is working correctly!")
            except asyncio.TimeoutError:
                print("⏰ Timeout - Backend connected but no response to message")
                print("✅ WebSocket connection works, backend may not respond to this message type")
            
    except Exception as e:
        if "ConnectionRefused" in str(type(e)) or "Connection refused" in str(e):
            print("❌ Connection refused - WebSocket server not running on port 5010")
        elif "TimeoutError" in str(type(e)):
            print("⏰ Timeout - No response from server")
        else:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())