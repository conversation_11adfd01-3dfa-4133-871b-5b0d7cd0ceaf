#!/usr/bin/env python3
"""
Test audio playback functionality by sending test audio to webapp
"""
import asyncio
import json
import websockets
import base64
import numpy as np

async def test_audio_playback():
    uri = "ws://localhost:5010"
    
    try:
        print(f"Testing audio playback functionality...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket server")
            
            # Register user
            register_message = {
                "type": "store_user", 
                "session": "playback_test",
                "data": {
                    "name": "Playback Test User",
                    "mobile": "webapp", 
                    "userId": "playback_test",
                    "sessionType": "call",
                    "target": "english_tutor"
                }
            }
            
            await websocket.send(json.dumps(register_message))
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"✅ User registered: {json.loads(response)}")
            
            # Generate test audio for playback (simulate AI response)
            # Create a clear 440Hz tone for testing
            sample_rate = 8000
            duration = 2.0  # 2 seconds
            frequency = 440  # A4 note
            
            # Generate sine wave
            t = np.linspace(0, duration, int(sample_rate * duration))
            signal = np.sin(2 * np.pi * frequency * t) * 0.7  # Amplitude 0.7
            
            # Convert to different audio formats to test webapp's audio decoder
            
            # Test 1: 16-bit PCM (little-endian)
            print("\n=== Testing 16-bit PCM Audio ===")
            signal_16bit = (signal * 32767).astype(np.int16)
            audio_bytes_16bit = signal_16bit.tobytes()
            audio_b64_16bit = base64.b64encode(audio_bytes_16bit).decode('utf-8')
            
            # Simulate backend response
            test_message_16bit = {
                "type": "llm_answer",
                "data": audio_b64_16bit
            }
            
            print(f"📤 Sending 16-bit test audio: {len(audio_bytes_16bit)} bytes -> {len(audio_b64_16bit)} base64 chars")
            print(f"   Sample values: {list(signal_16bit[:10])}")
            
            # Test 2: 8-bit PCM (unsigned)
            print("\n=== Testing 8-bit PCM Audio ===")
            signal_8bit = ((signal + 1) * 127.5).astype(np.uint8)
            audio_bytes_8bit = signal_8bit.tobytes()
            audio_b64_8bit = base64.b64encode(audio_bytes_8bit).decode('utf-8')
            
            test_message_8bit = {
                "type": "llm_answer", 
                "data": audio_b64_8bit
            }
            
            print(f"📤 Sending 8-bit test audio: {len(audio_bytes_8bit)} bytes -> {len(audio_b64_8bit)} base64 chars")
            print(f"   Sample values: {list(signal_8bit[:10])}")
            
            # Test 3: Float32 PCM
            print("\n=== Testing Float32 PCM Audio ===")
            signal_float32 = signal.astype(np.float32)
            audio_bytes_float32 = signal_float32.tobytes()
            audio_b64_float32 = base64.b64encode(audio_bytes_float32).decode('utf-8')
            
            test_message_float32 = {
                "type": "llm_answer",
                "data": audio_b64_float32
            }
            
            print(f"📤 Sending float32 test audio: {len(audio_bytes_float32)} bytes -> {len(audio_b64_float32)} base64 chars")
            print(f"   Sample values: {list(signal_float32[:10])}")
            
            # Save test files for manual verification
            with open('/tmp/test_audio_16bit.raw', 'wb') as f:
                f.write(audio_bytes_16bit)
            with open('/tmp/test_audio_8bit.raw', 'wb') as f:
                f.write(audio_bytes_8bit)
            with open('/tmp/test_audio_float32.raw', 'wb') as f:
                f.write(audio_bytes_float32)
                
            print(f"\n✅ Test audio files saved to /tmp/")
            print(f"   - /tmp/test_audio_16bit.raw ({len(audio_bytes_16bit)} bytes)")
            print(f"   - /tmp/test_audio_8bit.raw ({len(audio_bytes_8bit)} bytes)") 
            print(f"   - /tmp/test_audio_float32.raw ({len(audio_bytes_float32)} bytes)")
            
            print(f"\n📝 To test webapp audio playback:")
            print(f"   1. Open http://localhost:1800 in browser")
            print(f"   2. Click 'Test Audio Playback' button")
            print(f"   3. Check debug console for audio decoding logs")
            print(f"   4. Listen for test tone playback")
            
            print(f"\n✅ Audio playback test data prepared successfully")
            
    except Exception as e:
        print(f"❌ Audio playback test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_audio_playback())