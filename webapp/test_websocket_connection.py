#!/usr/bin/env python3
"""
Simple WebSocket connection test
"""
import asyncio
import json
import websockets

async def test_websocket_connection():
    uri = "ws://localhost:5010"
    
    try:
        print(f"Testing WebSocket connection to {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket server")
            
            # Test user registration
            register_message = {
                "type": "store_user", 
                "session": "test_session",
                "data": {
                    "name": "Test User",
                    "mobile": "test",
                    "userId": "test_session",
                    "sessionType": "call",
                    "target": "english_tutor"
                }
            }
            
            await websocket.send(json.dumps(register_message))
            print("📝 Sent user registration")
            
            # Wait for response with timeout
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"✅ Registration response: {response_data}")
                
                # Test start AI call
                start_call_message = {
                    "type": "start_ai_call",
                    "session": "test_session"
                }
                await websocket.send(json.dumps(start_call_message))
                print("📞 Sent start AI call")
                
                # End call
                end_call_message = {
                    "type": "end_ai_call",
                    "session": "test_session"
                }
                await websocket.send(json.dumps(end_call_message))
                print("📞 Sent end AI call")
                
                print("✅ WebSocket server is working correctly")
                
            except asyncio.TimeoutError:
                print("⚠️ No response from server, but connection established")
                
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket_connection())