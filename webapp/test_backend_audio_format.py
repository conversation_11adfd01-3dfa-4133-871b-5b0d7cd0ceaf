#!/usr/bin/env python3
"""
Test script to verify the exact audio format sent by the backend.
This helps debug audio playback issues in the webapp.
"""

import sys
import os
import base64
import struct

# Add the parent directory to the path to import tutor modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tutor.modules.audio.text_speech import TextToSpeech

def analyze_audio_format():
    """Analyze the exact format of audio generated by the backend TTS."""
    
    print("🔍 Analyzing Backend Audio Format")
    print("=" * 50)
    
    # Initialize TTS
    tts = TextToSpeech()
    
    # Generate a simple test phrase
    test_text = "Hello, this is a test."
    print(f"📝 Generating audio for: '{test_text}'")
    
    # Generate audio streams
    pcm_stream, wav_stream = tts.generate_audio(test_text)
    
    # Read the PCM stream (this is what gets sent to webapp)
    pcm_stream.seek(0)
    pcm_data = pcm_stream.read()
    
    print(f"\n📊 Audio Analysis:")
    print(f"   Total PCM bytes: {len(pcm_data)}")
    print(f"   Expected samples: {len(pcm_data) // 2} (16-bit)")
    print(f"   Expected duration: {(len(pcm_data) // 2) / 44100:.3f} seconds")
    
    # Analyze first few samples
    print(f"\n🔬 First 10 samples analysis:")
    for i in range(min(10, len(pcm_data) // 2)):
        byte_offset = i * 2
        
        # Try little-endian (most likely)
        sample_le = struct.unpack('<h', pcm_data[byte_offset:byte_offset+2])[0]
        
        # Try big-endian (less likely)
        sample_be = struct.unpack('>h', pcm_data[byte_offset:byte_offset+2])[0]
        
        # Raw bytes
        byte1 = pcm_data[byte_offset]
        byte2 = pcm_data[byte_offset + 1]
        
        print(f"   Sample {i}: bytes=[{byte1:3d}, {byte2:3d}] LE={sample_le:6d} BE={sample_be:6d}")
    
    # Test base64 encoding (like backend does)
    print(f"\n📦 Base64 Encoding Test:")
    
    # Take first 512 bytes (typical chunk size)
    chunk_size = min(512, len(pcm_data))
    test_chunk = pcm_data[:chunk_size]
    
    # Encode to base64
    encoded = base64.b64encode(test_chunk).decode('utf-8')
    
    print(f"   Chunk size: {len(test_chunk)} bytes")
    print(f"   Base64 length: {len(encoded)} characters")
    print(f"   Base64 sample: {encoded[:50]}...")
    
    # Decode back and verify
    decoded = base64.b64decode(encoded)
    print(f"   Decode verification: {'✅ OK' if decoded == test_chunk else '❌ FAILED'}")
    
    # Analyze amplitude range
    print(f"\n📈 Amplitude Analysis:")
    samples = []
    for i in range(0, len(pcm_data), 2):
        if i + 1 < len(pcm_data):
            sample = struct.unpack('<h', pcm_data[i:i+2])[0]
            samples.append(sample)
    
    if samples:
        min_sample = min(samples)
        max_sample = max(samples)
        avg_abs = sum(abs(s) for s in samples) / len(samples)
        
        print(f"   Sample range: {min_sample} to {max_sample}")
        print(f"   Average absolute: {avg_abs:.1f}")
        print(f"   Peak amplitude: {max(abs(min_sample), abs(max_sample)) / 32768.0:.3f}")
    
    # Generate webapp test data
    print(f"\n🌐 Webapp Test Data:")
    print(f"   Use this in browser console to test:")
    print(f"   const testAudio = '{encoded[:200]}...';")
    print(f"   // This represents {len(test_chunk)} bytes of {test_text}")
    
    return {
        'pcm_data': pcm_data,
        'chunk_size': chunk_size,
        'encoded': encoded,
        'samples': samples[:10] if samples else []
    }

if __name__ == "__main__":
    try:
        result = analyze_audio_format()
        print(f"\n✅ Analysis complete!")
        
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
