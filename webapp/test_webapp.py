#!/usr/bin/env python3
"""
Simple test to check if webapp is accessible and working
"""
import requests
import json

def test_webapp():
    try:
        # Test if webapp is accessible
        response = requests.get('http://localhost:1800')
        print(f"✅ Webapp accessible: Status {response.status_code}")
        
        # Test health endpoint
        health_response = requests.get('http://localhost:1800/health')
        print(f"✅ Health endpoint: {health_response.json()}")
        
        # Test start-call endpoint
        start_response = requests.post('http://localhost:1800/start-call')
        print(f"✅ Start-call endpoint: {start_response.json()}")
        
        # Test end-call endpoint  
        end_response = requests.post('http://localhost:1800/end-call')
        print(f"✅ End-call endpoint: {end_response.json()}")
        
        print("✅ All webapp endpoints are working correctly")
        
    except Exception as e:
        print(f"❌ Webapp test failed: {e}")

if __name__ == "__main__":
    test_webapp()