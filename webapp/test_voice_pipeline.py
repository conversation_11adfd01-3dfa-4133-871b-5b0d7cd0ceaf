#!/usr/bin/env python3
"""
Test the complete voice calling pipeline including audio format compatibility
"""
import asyncio
import json
import websockets
import base64
import numpy as np
import wave
import time

async def test_voice_pipeline():
    uri = "ws://localhost:5010"
    
    try:
        print(f"Testing complete voice calling pipeline with {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket server")
            
            # Step 1: Register user like webapp does
            register_message = {
                "type": "store_user", 
                "session": "pipeline_test",
                "data": {
                    "name": "Pipeline Test User",
                    "mobile": "webapp", 
                    "userId": "pipeline_test",
                    "sessionType": "call",
                    "target": "english_tutor"
                }
            }
            
            await websocket.send(json.dumps(register_message))
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"✅ User registered: {json.loads(response)}")
            
            # Step 2: Start AI call
            start_call_message = {
                "type": "start_ai_call",
                "session": "pipeline_test"
            }
            await websocket.send(json.dumps(start_call_message))
            print("📞 Started AI call")
            
            # Wait a bit
            await asyncio.sleep(0.5)
            
            # Step 3: Set AI listening
            listening_message = {
                "type": "ai_start_listening",
                "session": "pipeline_test"
            }
            await websocket.send(json.dumps(listening_message))
            print("🎤 Set AI listening")
            
            # Wait a bit more
            await asyncio.sleep(0.3)
            
            # Step 4: Send audio chunks exactly like webapp (8-bit format)
            print("📤 Sending 8-bit audio chunks (webapp format)...")
            
            # Generate test audio that simulates speech
            # Create a sine wave pattern with speech-like characteristics
            sample_rate = 8000
            duration = 3.0  # 3 seconds of speech simulation
            frequency = 300  # Human speech fundamental frequency
            
            total_samples = int(sample_rate * duration)
            chunk_size = 1024  # Same as webapp
            
            audio_sent = False
            ai_response_received = False
            
            for chunk_start in range(0, total_samples, chunk_size):
                chunk_end = min(chunk_start + chunk_size, total_samples)
                chunk_length = chunk_end - chunk_start
                
                # Generate sine wave with speech-like envelope
                t = np.linspace(chunk_start/sample_rate, chunk_end/sample_rate, chunk_length)
                
                # Add some harmonics and envelope for more realistic speech
                signal = (np.sin(2 * np.pi * frequency * t) * 0.5 + 
                         np.sin(2 * np.pi * frequency * 2 * t) * 0.2 +
                         np.sin(2 * np.pi * frequency * 3 * t) * 0.1)
                
                # Apply envelope (speech-like amplitude modulation)
                envelope = np.exp(-0.1 * t) * (1 + 0.3 * np.sin(10 * t))
                signal = signal * envelope
                
                # Convert to 8-bit unsigned (0-255) exactly like webapp
                signal_clamped = np.clip(signal, -1, 1)
                audio_8bit = np.round((signal_clamped + 1) * 127.5).astype(np.uint8)
                
                # Send as regular Python list (not numpy array)
                audio_data = audio_8bit.tolist()
                
                audio_message = {
                    "type": "audio_chunk",
                    "session": "pipeline_test",
                    "data": audio_data
                }
                
                await websocket.send(json.dumps(audio_message))
                print(f"📤 Sent chunk {chunk_start//chunk_size + 1}/{(total_samples + chunk_size - 1)//chunk_size} "
                      f"({len(audio_data)} samples, range: {min(audio_data)}-{max(audio_data)})")
                
                audio_sent = True
                
                # Check for AI responses
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=0.01)
                        data = json.loads(response)
                        if data.get("type") == "llm_answer":
                            audio_length = len(data.get("data", ""))
                            print(f"🎵 Received AI audio response: {audio_length} bytes (base64)")
                            ai_response_received = True
                            
                            # Test audio data format
                            try:
                                audio_b64 = data.get("data", "")
                                if audio_b64:
                                    # Decode like webapp does
                                    audio_bytes = base64.b64decode(audio_b64)
                                    print(f"   Decoded to {len(audio_bytes)} raw bytes")
                                    
                                    # Check first few bytes
                                    sample_bytes = list(audio_bytes[:10])
                                    print(f"   Sample bytes: {sample_bytes}")
                                    
                                    # Save a sample for analysis
                                    if len(audio_bytes) > 100:
                                        with open('/tmp/ai_audio_sample.raw', 'wb') as f:
                                            f.write(audio_bytes)
                                        print(f"   ✅ Saved audio sample to /tmp/ai_audio_sample.raw")
                                        
                            except Exception as decode_error:
                                print(f"   ❌ Audio decode error: {decode_error}")
                                
                        else:
                            print(f"📨 Other message: {data.get('type')}")
                            
                except asyncio.TimeoutError:
                    pass  # No more responses, continue
                
                # Send chunks every 100ms like real-time
                await asyncio.sleep(0.1)
            
            print(f"✅ Completed sending {total_samples} audio samples in 8-bit format")
            
            # Wait for any remaining AI responses
            print("⏳ Waiting for final AI responses...")
            timeout_count = 0
            while timeout_count < 50:  # Wait up to 5 seconds
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=0.1)
                    data = json.loads(response)
                    if data.get("type") == "llm_answer":
                        audio_length = len(data.get("data", ""))
                        print(f"🎵 Final AI audio response: {audio_length} bytes")
                        ai_response_received = True
                except asyncio.TimeoutError:
                    timeout_count += 1
                    
            # Step 5: End AI call
            end_call_message = {
                "type": "end_ai_call",
                "session": "pipeline_test"
            }
            await websocket.send(json.dumps(end_call_message))
            print("📞 Ended AI call")
            
            # Summary
            print("\n=== PIPELINE TEST SUMMARY ===")
            print(f"✅ WebSocket connection: SUCCESS")
            print(f"✅ User registration: SUCCESS") 
            print(f"✅ AI call start: SUCCESS")
            print(f"✅ Audio transmission: {'SUCCESS' if audio_sent else 'FAILED'}")
            print(f"✅ AI audio response: {'SUCCESS' if ai_response_received else 'FAILED'}")
            
            if audio_sent and ai_response_received:
                print("🎉 COMPLETE VOICE PIPELINE: SUCCESS")
                print("   - Backend successfully receives 8-bit audio chunks")
                print("   - AI processes audio and sends back responses")
                print("   - Audio format is compatible with webapp implementation")
            else:
                print("❌ PIPELINE ISSUES DETECTED")
                if not audio_sent:
                    print("   - Audio transmission failed")
                if not ai_response_received:
                    print("   - No AI audio responses received")
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_voice_pipeline())