#!/usr/bin/env python3
"""
Test audio format compatibility with backend
"""
import asyncio
import json
import websockets

async def test_audio_format():
    uri = "ws://localhost:5010"
    
    try:
        print(f"Testing audio format with backend at {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to backend")
            
            # Register user first
            register_message = {
                "type": "store_user", 
                "session": "audio_test_session",
                "data": {
                    "name": "Audio Test User",
                    "mobile": "test_audio",
                    "userId": "audio_test_session",
                    "sessionType": "call",
                    "target": "english_tutor"
                }
            }
            
            await websocket.send(json.dumps(register_message))
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"📝 User registration: {json.loads(response)}")
            
            # Send test audio chunk (8-bit format: 0-255 values)
            test_audio_data = [127, 130, 125, 128, 132, 124, 129]  # Sample 8-bit audio values
            audio_message = {
                "type": "audio_chunk",
                "session": "audio_test_session", 
                "data": test_audio_data
            }
            
            await websocket.send(json.dumps(audio_message))
            print(f"📤 Sent 8-bit audio chunk: {test_audio_data}")
            
            # Wait for potential audio response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                data = json.loads(response)
                if data.get("type") == "llm_answer":
                    audio_length = len(data.get("data", ""))
                    print(f"📥 Received AI audio response: {audio_length} bytes")
                    print("✅ Audio format test successful!")
                else:
                    print(f"📥 Received other response: {data.get('type')}")
            except asyncio.TimeoutError:
                print("⏰ No audio response within 10 seconds (this may be normal)")
                print("✅ Audio chunk was sent successfully to backend")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_audio_format())