/* Custom styles for Voice Calling Application */

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.2s ease-in-out;
}

/* Custom button hover effects */
.btn-hover {
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Enhanced status indicators */
.status-indicator {
    position: relative;
    overflow: hidden;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
}

.status-connected::before {
    background: radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%);
    animation: pulse-glow 2s infinite;
}

.status-connecting::before {
    background: radial-gradient(circle, rgba(245, 158, 11, 0.4) 0%, transparent 70%);
    animation: pulse-glow 1s infinite;
}

.status-disconnected::before {
    background: radial-gradient(circle, rgba(239, 68, 68, 0.4) 0%, transparent 70%);
}

@keyframes pulse-glow {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.1;
    }
}

/* Enhanced audio level bar */
.audio-level {
    position: relative;
    overflow: hidden;
}

.audio-level::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Debug console improvements */
.debug-console {
    border: 1px solid #e5e7eb;
    background: linear-gradient(to bottom, #f9fafb, #f3f4f6);
}

.debug-console::-webkit-scrollbar {
    width: 8px;
}

.debug-console::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.debug-console::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.debug-console::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Log entry animations */
.log-entry {
    animation: fadeInUp 0.3s ease-out;
    border-left: 3px solid transparent;
}

.log-info {
    border-left-color: #3b82f6;
}

.log-warning {
    border-left-color: #f59e0b;
}

.log-error {
    border-left-color: #ef4444;
}

.log-success {
    border-left-color: #10b981;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button loading state */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .debug-console {
        font-size: 11px;
        max-height: 150px;
    }
    
    .grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Accessibility improvements */
.focus\:ring-2:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .status-indicator {
        border: 2px solid currentColor;
    }
    
    .debug-console {
        border: 2px solid #000;
    }
    
    .log-entry {
        border-left-width: 4px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .debug-console {
        border: 1px solid #000;
        background: white;
        max-height: none;
        overflow: visible;
    }
    
    .log-entry {
        page-break-inside: avoid;
        color: #000 !important;
        background: transparent !important;
    }
    
    button {
        display: none;
    }
}