/**
 * Audio Quality Test Script
 * Test the improved audio processing and playback quality
 */

class AudioQualityTest {
    constructor() {
        this.testResults = [];
        this.audioContext = null;
        this.testAudioData = null;
    }

    async initialize() {
        try {
            // Create audio context for testing
            const AudioContextClass = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContextClass({
                sampleRate: 44100,
                latencyHint: 'playback'
            });

            // Generate test audio data (sine wave)
            this.generateTestAudio();
            
            console.log('Audio Quality Test initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize audio test:', error);
            return false;
        }
    }

    generateTestAudio() {
        // Generate a 1-second sine wave at 440Hz (A4 note)
        const sampleRate = 44100;
        const duration = 1; // 1 second
        const frequency = 440; // A4 note
        const numSamples = sampleRate * duration;
        
        // Create 16-bit PCM data
        const audioData = new Uint8Array(numSamples * 2);
        
        for (let i = 0; i < numSamples; i++) {
            // Generate sine wave sample
            const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate);
            
            // Convert to 16-bit signed integer
            const sample16 = Math.round(sample * 32767 * 0.5); // 50% volume
            
            // Convert to little-endian bytes
            const lowByte = sample16 & 0xFF;
            const highByte = (sample16 >> 8) & 0xFF;
            
            audioData[i * 2] = lowByte;
            audioData[i * 2 + 1] = highByte;
        }
        
        this.testAudioData = audioData;
        console.log(`Generated test audio: ${audioData.length} bytes`);
    }

    async testAudioProcessing() {
        if (!this.testAudioData) {
            console.error('No test audio data available');
            return false;
        }

        try {
            // Test the audio processing pipeline
            const startTime = performance.now();
            
            // Simulate the audio processing that happens in voice-call-engine.js
            const sampleRate = 44100;
            const bytesPerSample = 2;
            const numSamples = Math.floor(this.testAudioData.length / bytesPerSample);
            
            // Create audio buffer
            const audioBuffer = this.audioContext.createBuffer(1, numSamples, sampleRate);
            const channelData = audioBuffer.getChannelData(0);
            
            // Process audio data (similar to playPCMAudio method)
            let maxAmplitude = 0;
            for (let i = 0; i < numSamples; i++) {
                const byteIndex = i * bytesPerSample;
                if (byteIndex + 1 < this.testAudioData.length) {
                    const lowByte = this.testAudioData[byteIndex];
                    const highByte = this.testAudioData[byteIndex + 1];
                    
                    let sample = (highByte << 8) | lowByte;
                    if (sample >= 32768) {
                        sample -= 65536;
                    }
                    
                    const normalizedSample = sample / 32768.0;
                    channelData[i] = normalizedSample;
                    maxAmplitude = Math.max(maxAmplitude, Math.abs(normalizedSample));
                }
            }
            
            const processingTime = performance.now() - startTime;
            
            const result = {
                test: 'Audio Processing',
                processingTime: processingTime.toFixed(2) + 'ms',
                numSamples: numSamples,
                maxAmplitude: maxAmplitude.toFixed(3),
                success: true
            };
            
            this.testResults.push(result);
            console.log('Audio processing test completed:', result);
            return true;
            
        } catch (error) {
            console.error('Audio processing test failed:', error);
            this.testResults.push({
                test: 'Audio Processing',
                error: error.message,
                success: false
            });
            return false;
        }
    }

    async testChunkSizes() {
        const chunkSizes = [512, 1024, 2048, 4096];
        
        for (const chunkSize of chunkSizes) {
            try {
                const startTime = performance.now();
                
                // Simulate chunking the test audio
                const chunks = [];
                for (let i = 0; i < this.testAudioData.length; i += chunkSize) {
                    const chunk = this.testAudioData.slice(i, i + chunkSize);
                    chunks.push(chunk);
                }
                
                const chunkingTime = performance.now() - startTime;
                
                const result = {
                    test: `Chunk Size ${chunkSize}`,
                    chunkingTime: chunkingTime.toFixed(2) + 'ms',
                    numChunks: chunks.length,
                    avgChunkSize: Math.round(this.testAudioData.length / chunks.length),
                    success: true
                };
                
                this.testResults.push(result);
                console.log(`Chunk size test (${chunkSize}) completed:`, result);
                
            } catch (error) {
                console.error(`Chunk size test (${chunkSize}) failed:`, error);
                this.testResults.push({
                    test: `Chunk Size ${chunkSize}`,
                    error: error.message,
                    success: false
                });
            }
        }
    }

    async runAllTests() {
        console.log('Starting Audio Quality Tests...');
        
        if (!await this.initialize()) {
            console.error('Failed to initialize audio tests');
            return false;
        }
        
        await this.testAudioProcessing();
        await this.testChunkSizes();
        
        this.displayResults();
        return true;
    }

    displayResults() {
        console.log('\n=== Audio Quality Test Results ===');
        
        this.testResults.forEach((result, index) => {
            console.log(`\nTest ${index + 1}: ${result.test}`);
            if (result.success) {
                Object.keys(result).forEach(key => {
                    if (key !== 'test' && key !== 'success') {
                        console.log(`  ${key}: ${result[key]}`);
                    }
                });
            } else {
                console.log(`  ERROR: ${result.error}`);
            }
        });
        
        const successCount = this.testResults.filter(r => r.success).length;
        const totalCount = this.testResults.length;
        
        console.log(`\n=== Summary ===`);
        console.log(`Tests passed: ${successCount}/${totalCount}`);
        console.log(`Success rate: ${((successCount / totalCount) * 100).toFixed(1)}%`);
        
        if (successCount === totalCount) {
            console.log('🎉 All audio quality tests passed!');
        } else {
            console.log('⚠️ Some tests failed. Check the results above.');
        }
    }

    cleanup() {
        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}

// Export for use in browser console or testing
window.AudioQualityTest = AudioQualityTest;

// Auto-run tests if this script is loaded directly
if (typeof window !== 'undefined' && window.location.pathname.includes('audio-test')) {
    const test = new AudioQualityTest();
    test.runAllTests().then(() => {
        console.log('Audio quality tests completed');
    }).catch(error => {
        console.error('Audio quality tests failed:', error);
    });
}
