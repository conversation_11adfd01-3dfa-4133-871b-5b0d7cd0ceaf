/**
 * UI Controller for Voice Calling Application
 * Manages user interface updates and debug console
 */
class UIController {
    constructor() {
        this.elements = this.initializeElements();
        this.autoScroll = true;
        this.maxLogEntries = 1000;
        this.logCount = 0;
        
        // Connection status
        this.connectionStatus = 'disconnected';
        this.callStatus = 'disconnected';
        
        // Initialize UI state
        this.updateCallButtons();
        this.updateSessionId('');
    }

    /**
     * Initialize DOM element references
     */
    initializeElements() {
        return {
            // Status indicators
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            wsStatus: document.getElementById('wsStatus'),
            sessionId: document.getElementById('sessionId'),
            
            // Call controls
            startCallBtn: document.getElementById('startCallBtn'),
            stopCallBtn: document.getElementById('stopCallBtn'),
            
            // Audio level
            audioLevelBar: document.getElementById('audioLevelBar'),
            audioLevelText: document.getElementById('audioLevelText'),
            
            // Stats
            packetsSent: document.getElementById('packetsSent'),
            packetsReceived: document.getElementById('packetsReceived'),
            
            // Debug console
            debugConsole: document.getElementById('debugConsole'),
            clearLogsBtn: document.getElementById('clearLogsBtn'),
            toggleAutoScrollBtn: document.getElementById('toggleAutoScrollBtn'),
            
            // Compatibility notice
            compatibilityNotice: document.getElementById('compatibilityNotice')
        };
    }

    /**
     * Update connection status
     */
    updateConnectionStatus(status) {
        this.connectionStatus = status;
        
        const indicator = this.elements.statusIndicator;
        const statusText = this.elements.statusText;
        const wsStatus = this.elements.wsStatus;
        
        // Remove all status classes
        indicator.className = 'status-indicator';
        
        switch (status) {
            case 'connected':
                indicator.classList.add('status-connected');
                statusText.textContent = 'Connected';
                statusText.className = 'ml-2 font-semibold text-green-600';
                wsStatus.textContent = 'Connected';
                wsStatus.className = 'font-medium text-green-600';
                break;
            case 'connecting':
                indicator.classList.add('status-connecting');
                statusText.textContent = 'Connecting...';
                statusText.className = 'ml-2 font-semibold text-yellow-600';
                wsStatus.textContent = 'Connecting';
                wsStatus.className = 'font-medium text-yellow-600';
                break;
            case 'disconnected':
            default:
                indicator.classList.add('status-disconnected');
                statusText.textContent = 'Disconnected';
                statusText.className = 'ml-2 font-semibold text-red-600';
                wsStatus.textContent = 'Disconnected';
                wsStatus.className = 'font-medium text-red-600';
                break;
        }
        
        this.updateCallButtons();
    }

    /**
     * Update call status
     */
    updateCallStatus(status) {
        this.callStatus = status;
        this.updateCallButtons();
        
        switch (status) {
            case 'connected':
                this.log('Call established successfully', 'success');
                break;
            case 'connecting':
                this.log('Establishing call connection...', 'info');
                break;
            case 'disconnected':
                this.log('Call disconnected', 'info');
                break;
            case 'error':
                this.log('Call connection failed', 'error');
                break;
        }
    }

    /**
     * Update call control buttons state
     */
    updateCallButtons() {
        const startBtn = this.elements.startCallBtn;
        const stopBtn = this.elements.stopCallBtn;
        
        const isConnected = this.connectionStatus === 'connected';
        const isCallActive = this.callStatus === 'connected' || this.callStatus === 'connecting';
        
        // Start button
        startBtn.disabled = !isConnected || isCallActive;
        if (startBtn.disabled) {
            startBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            startBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
        
        // Stop button
        stopBtn.disabled = !isCallActive;
        if (stopBtn.disabled) {
            stopBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            stopBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    /**
     * Update audio level indicator
     */
    updateAudioLevel(level) {
        const levelBar = this.elements.audioLevelBar;
        const levelText = this.elements.audioLevelText;
        
        const clampedLevel = Math.max(0, Math.min(100, level));
        
        levelBar.style.width = `${clampedLevel}%`;
        levelText.textContent = `${Math.round(clampedLevel)}%`;
        
        // Update color based on level
        if (clampedLevel > 80) {
            levelBar.style.background = '#ef4444'; // Red
        } else if (clampedLevel > 50) {
            levelBar.style.background = 'linear-gradient(to right, #10b981, #f59e0b, #ef4444)';
        } else {
            levelBar.style.background = '#10b981'; // Green
        }
    }

    /**
     * Update session ID display
     */
    updateSessionId(sessionId) {
        this.elements.sessionId.textContent = sessionId || '-';
    }

    /**
     * Update statistics display
     */
    updateStats(stats) {
        this.elements.packetsSent.textContent = stats.packetsSent || 0;
        this.elements.packetsReceived.textContent = stats.packetsReceived || 0;
    }

    /**
     * Add a log entry to the debug console
     */
    log(message, type = 'info') {
        const debugConsole = this.elements.debugConsole;
        const timestamp = new Date().toLocaleTimeString();
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        debugConsole.appendChild(logEntry);
        this.logCount++;
        
        // Limit log entries to prevent memory issues
        if (this.logCount > this.maxLogEntries) {
            const firstEntry = debugConsole.firstElementChild;
            if (firstEntry) {
                debugConsole.removeChild(firstEntry);
                this.logCount--;
            }
        }
        
        // Auto-scroll if enabled
        if (this.autoScroll) {
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        // Also log to browser console for debugging
        const logMethod = type === 'error' ? console.error : 
                         type === 'warning' ? console.warn : 
                         console.log;
        logMethod(`[VoiceApp] ${message}`);
    }

    /**
     * Clear debug console logs
     */
    clearLogs() {
        this.elements.debugConsole.innerHTML = '';
        this.logCount = 0;
        this.log('Debug console cleared', 'info');
    }

    /**
     * Toggle auto-scroll for debug console
     */
    toggleAutoScroll() {
        this.autoScroll = !this.autoScroll;
        const btn = this.elements.toggleAutoScrollBtn;
        btn.textContent = `Auto-scroll: ${this.autoScroll ? 'ON' : 'OFF'}`;
        
        if (this.autoScroll) {
            btn.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
            btn.classList.add('bg-blue-200', 'hover:bg-blue-300', 'text-blue-700');
        } else {
            btn.classList.remove('bg-blue-200', 'hover:bg-blue-300', 'text-blue-700');
            btn.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
        }
        
        this.log(`Auto-scroll ${this.autoScroll ? 'enabled' : 'disabled'}`, 'info');
    }

    /**
     * Show error alert
     */
    showError(message, details = '') {
        this.log(`ERROR: ${message}${details ? ' - ' + details : ''}`, 'error');
        
        // Create error alert
        const alert = document.createElement('div');
        alert.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-md';
        alert.innerHTML = `
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-semibold">Error</h4>
                    <p class="text-sm mt-1">${message}</p>
                    ${details ? `<p class="text-xs mt-1 opacity-90">${details}</p>` : ''}
                </div>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.log(`SUCCESS: ${message}`, 'success');
        
        // Create success alert
        const alert = document.createElement('div');
        alert.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-md';
        alert.innerHTML = `
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-semibold">Success</h4>
                    <p class="text-sm mt-1">${message}</p>
                </div>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(alert);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 3000);
    }

    /**
     * Show browser compatibility notice
     */
    showCompatibilityNotice(missingFeatures) {
        const notice = this.elements.compatibilityNotice;
        if (missingFeatures && missingFeatures.length > 0) {
            notice.classList.remove('hidden');
            const featuresList = missingFeatures.join(', ');
            notice.querySelector('p').textContent = 
                `Your browser is missing support for: ${featuresList}. Some features may not work properly.`;
        }
    }

    /**
     * Update WebSocket connection info
     */
    updateWebSocketInfo(status) {
        if (status.sessionId) {
            this.updateSessionId(status.sessionId);
        }
        
        if (status.stats) {
            this.updateStats(status.stats);
        }
    }

    /**
     * Handle permission requests
     */
    async requestMicrophonePermission() {
        try {
            this.log('Requesting microphone permission...', 'info');
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop()); // Stop the test stream
            this.log('Microphone permission granted', 'success');
            return true;
        } catch (error) {
            this.showError('Microphone permission denied', 'Please allow microphone access to use voice calling features.');
            this.log(`Microphone permission error: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * Get current UI state
     */
    getState() {
        return {
            connectionStatus: this.connectionStatus,
            callStatus: this.callStatus,
            autoScroll: this.autoScroll,
            logCount: this.logCount
        };
    }

    /**
     * Reset UI to initial state
     */
    reset() {
        this.updateConnectionStatus('disconnected');
        this.updateCallStatus('disconnected');
        this.updateAudioLevel(0);
        this.updateSessionId('');
        this.updateStats({ packetsSent: 0, packetsReceived: 0 });
        this.clearLogs();
        this.log('UI reset to initial state', 'info');
    }
}