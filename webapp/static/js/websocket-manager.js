/**
 * WebSocket Manager for Voice Calling Application
 * Handles connection to the existing backend WebSocket server
 */
class WebSocketManager {
    constructor(url) {
        this.url = url;
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.sessionId = this.generateSessionId();
        this.messageHandlers = new Map();
        this.stats = {
            packetsSent: 0,
            packetsReceived: 0,
            connectionStartTime: null
        };
        
        // Bind methods to preserve context
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }

    /**
     * Generate a unique session ID for this webapp instance
     */
    generateSessionId() {
        return 'webapp_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * Connect to the WebSocket server
     */
    async connect() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            try {
                this.socket = new WebSocket(this.url);
                
                this.socket.onopen = (event) => {
                    this.onOpen(event);
                    resolve();
                };
                
                this.socket.onmessage = this.onMessage;
                this.socket.onclose = this.onClose;
                this.socket.onerror = (event) => {
                    this.onError(event);
                    reject(new Error('WebSocket connection failed'));
                };

                // Set connection timeout
                setTimeout(() => {
                    if (this.socket.readyState !== WebSocket.OPEN) {
                        this.socket.close();
                        reject(new Error('WebSocket connection timeout'));
                    }
                }, 10000);

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Disconnect from the WebSocket server
     */
    disconnect() {
        if (this.socket) {
            this.socket.close(1000, 'User initiated disconnect');
        }
    }

    /**
     * Send a message to the WebSocket server
     */
    send(data) {
        if (!this.isConnected) {
            throw new Error('WebSocket not connected');
        }

        try {
            const message = JSON.stringify(data);
            this.socket.send(message);
            this.stats.packetsSent++;
            
            // Log non-audio messages for debugging
            if (data.type !== 'audio_chunk') {
                this.emit('log', `Sent: ${data.type}`, 'info');
            }
            
            this.emit('stats-update', this.stats);
        } catch (error) {
            this.emit('log', `Send error: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Send audio chunk to the backend
     */
    sendAudioChunk(audioData) {
        // Ensure audioData is a regular array of 8-bit values (0-255)
        const dataArray = Array.isArray(audioData) ? audioData : Array.from(audioData);
        
        // Debug: Log first few values occasionally to verify format
        if (Math.random() < 0.01) {
            const sample = dataArray.slice(0, 5);
            this.emit('log', `Sending 8-bit audio: [${sample.join(', ')}...] (${dataArray.length} samples)`, 'info');
        }
        
        const message = {
            type: 'audio_chunk',
            session: this.sessionId,
            data: dataArray
        };
        this.send(message);
    }

    /**
     * Register user with the backend
     */
    registerUser(name = 'WebApp User') {
        const message = {
            type: 'store_user',
            session: this.sessionId,
            data: {
                name: name,
                mobile: 'webapp',
                userId: this.sessionId,
                sessionType: 'call',
                target: 'english_tutor'  // Valid target for BotManager
            }
        };
        this.send(message);
    }

    /**
     * Start AI call
     */
    startAICall() {
        const message = {
            type: 'start_ai_call',
            session: this.sessionId
        };
        this.send(message);
    }

    /**
     * End AI call
     */
    endAICall() {
        const message = {
            type: 'end_ai_call',
            session: this.sessionId
        };
        this.send(message);
    }

    /**
     * Set AI listening state
     */
    setAIListening(listening) {
        if (listening) {
            const message = {
                type: 'ai_start_listening',
                session: this.sessionId
            };
            this.send(message);
        }
    }

    /**
     * Register a message handler for specific message types
     */
    on(messageType, handler) {
        if (!this.messageHandlers.has(messageType)) {
            this.messageHandlers.set(messageType, []);
        }
        this.messageHandlers.get(messageType).push(handler);
    }

    /**
     * Remove a message handler
     */
    off(messageType, handler) {
        const handlers = this.messageHandlers.get(messageType);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Emit an event to registered handlers
     */
    emit(messageType, ...args) {
        const handlers = this.messageHandlers.get(messageType);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(...args);
                } catch (error) {
                    console.error(`Error in ${messageType} handler:`, error);
                }
            });
        }
    }

    /**
     * Handle WebSocket connection open
     */
    onOpen(event) {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.stats.connectionStartTime = Date.now();
        
        this.emit('connected');
        this.emit('log', 'WebSocket connected successfully', 'success');
        
        // Register user with backend
        setTimeout(() => {
            this.registerUser();
        }, 100);
    }

    /**
     * Handle incoming WebSocket messages
     */
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.stats.packetsReceived++;
            
            // Log non-audio messages for debugging
            if (data.type !== 'audio_chunk' && data.type !== 'audio_response' && data.type !== 'llm_answer') {
                this.emit('log', `Received: ${data.type} - ${JSON.stringify(data.data)}`, 'info');
            } else if (data.type === 'llm_answer') {
                // Log llm_answer occasionally to show audio is being received
                if (Math.random() < 0.05) {
                    const dataType = typeof data.data;
                    const dataLength = data.data ? data.data.length || 'unknown' : 'null';
                    this.emit('log', `Received AI audio: ${dataType}, length: ${dataLength}`, 'info');
                }
            }

            this.emit('stats-update', this.stats);
            this.emit('message', data);
            
            // Handle specific message types
            switch (data.type) {
                case 'store_user':
                    this.emit('user-registered', data.data);
                    this.emit('log', `User registration: ${data.data}`, 'success');
                    break;
                case 'audio_response':
                case 'audio_chunk':
                case 'llm_answer':  // Backend sends audio as llm_answer
                    this.emit('audio-data', data.data);
                    break;
                case 'error':
                    this.emit('log', `Server error: ${data.message}`, 'error');
                    break;
                default:
                    this.emit('log', `Unknown message type: ${data.type}`, 'warning');
            }
            
        } catch (error) {
            this.emit('log', `Message parse error: ${error.message}`, 'error');
        }
    }

    /**
     * Handle WebSocket connection close
     */
    onClose(event) {
        this.isConnected = false;
        this.emit('disconnected', event.code, event.reason);
        
        if (event.code !== 1000) { // Not a normal closure
            this.emit('log', `WebSocket closed unexpectedly: ${event.code} ${event.reason}`, 'warning');
            this.attemptReconnect();
        } else {
            this.emit('log', 'WebSocket disconnected', 'info');
        }
    }

    /**
     * Handle WebSocket errors
     */
    onError(event) {
        this.emit('log', 'WebSocket error occurred', 'error');
        this.emit('error', event);
    }

    /**
     * Attempt to reconnect to the WebSocket server
     */
    async attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.emit('log', 'Max reconnection attempts reached', 'error');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        this.emit('log', `Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`, 'info');
        
        setTimeout(async () => {
            try {
                await this.connect();
                this.emit('log', 'Reconnected successfully', 'success');
            } catch (error) {
                this.emit('log', `Reconnection failed: ${error.message}`, 'error');
                this.attemptReconnect();
            }
        }, delay);
    }

    /**
     * Get connection status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            sessionId: this.sessionId,
            stats: this.stats,
            readyState: this.socket ? this.socket.readyState : WebSocket.CLOSED
        };
    }

    /**
     * Reset connection statistics
     */
    resetStats() {
        this.stats = {
            packetsSent: 0,
            packetsReceived: 0,
            connectionStartTime: this.stats.connectionStartTime
        };
        this.emit('stats-update', this.stats);
    }
}