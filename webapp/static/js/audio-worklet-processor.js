/**
 * AudioWorklet Processor for real-time audio processing
 * Replaces deprecated ScriptProcessorNode
 */
class AudioProcessor extends AudioWorkletProcessor {
    constructor() {
        super();
        this.sampleRate = 8000; // Target sample rate
        this.downsampleRatio = 48000 / this.sampleRate;
        this.downsampleBuffer = [];
        this.bufferIndex = 0;
        this.frameCount = 0;
        
        // Listen for messages from main thread
        this.port.onmessage = (event) => {
            if (event.data.type === 'setSampleRate') {
                this.downsampleRatio = event.data.sampleRate / this.sampleRate;
            }
        };
    }

    process(inputs, outputs, parameters) {
        const input = inputs[0];
        const output = outputs[0];

        if (input.length > 0) {
            const inputChannel = input[0];
            
            // Copy input to output (for monitoring)
            if (output.length > 0) {
                output[0].set(inputChannel);
            }

            // Process audio for sending to backend
            if (inputChannel.length > 0) {
                this.frameCount++;
                
                // Downsample audio
                const downsampledData = this.downsampleAudio(inputChannel);
                
                // Convert to 8-bit PCM (backend expects 8-bit!)
                const pcmData = this.convertToPCM8(downsampledData);
                
                // Send all audio data to main thread for buffering
                // Let main thread decide when to send to backend
                this.port.postMessage({
                    type: 'audioData',
                    data: pcmData,
                    frameCount: this.frameCount
                });
            }
        }

        return true; // Keep processor alive
    }

    downsampleAudio(inputData) {
        const outputLength = Math.ceil(inputData.length / this.downsampleRatio);
        const outputData = new Float32Array(outputLength);
        
        for (let i = 0; i < outputLength; i++) {
            const sourceIndex = Math.floor(i * this.downsampleRatio);
            outputData[i] = inputData[sourceIndex];
        }
        
        return outputData;
    }

    convertToPCM8(floatData) {
        const pcmData = [];
        
        for (let i = 0; i < floatData.length; i++) {
            // Apply gain boost for better AI processing
            let sample = floatData[i] * 3.0; // Boost signal by 3x
            // Clamp to [-1, 1] and convert to 8-bit unsigned (0-255)
            sample = Math.max(-1, Math.min(1, sample));
            // Convert from [-1, 1] to [0, 255]
            const unsignedSample = Math.round((sample + 1) * 127.5);
            pcmData.push(unsignedSample);
        }
        
        return pcmData; // Return regular array for WebSocket
    }
}

registerProcessor('audio-processor', AudioProcessor);