#!/usr/bin/env python3
"""
Test continuous audio streaming like mobile app
"""
import asyncio
import json
import websockets
import time

async def test_continuous_audio_streaming():
    uri = "ws://localhost:5010"
    
    try:
        print(f"Testing continuous audio streaming with backend at {uri}...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to backend")
            
            # Register user
            register_message = {
                "type": "store_user", 
                "session": "continuous_test_session",
                "data": {
                    "name": "Continuous Test User",
                    "mobile": "test_continuous",
                    "userId": "continuous_test_session",
                    "sessionType": "call",
                    "target": "english_tutor"
                }
            }
            
            await websocket.send(json.dumps(register_message))
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            print(f"📝 User registration: {json.loads(response)}")
            
            # Start AI call
            start_call_message = {
                "type": "start_ai_call",
                "session": "continuous_test_session"
            }
            await websocket.send(json.dumps(start_call_message))
            print("📞 Started AI call")
            
            # Wait a bit
            await asyncio.sleep(0.5)
            
            # Set AI listening
            listening_message = {
                "type": "ai_start_listening",
                "session": "continuous_test_session"
            }
            await websocket.send(json.dumps(listening_message))
            print("🎤 Set AI listening")
            
            # Wait a bit more
            await asyncio.sleep(0.3)
            
            # Send continuous audio chunks like mobile app
            print("📤 Sending continuous audio chunks...")
            for i in range(50):  # Send 50 chunks
                # Generate test audio data (1024 samples of 8-bit audio)
                # Simulate speech pattern with varying amplitude
                amplitude = 127 + int(30 * (i % 10) / 10)  # Varying amplitude
                test_audio_data = [amplitude + (j % 10) for j in range(1024)]
                
                audio_message = {
                    "type": "audio_chunk",
                    "session": "continuous_test_session",
                    "data": test_audio_data
                }
                
                await websocket.send(json.dumps(audio_message))
                print(f"📤 Sent chunk {i+1}/50 (amplitude: {amplitude})")
                
                # Send chunks every 100ms like real-time audio
                await asyncio.sleep(0.1)
                
                # Check for responses
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=0.01)
                    data = json.loads(response)
                    if data.get("type") == "llm_answer":
                        audio_length = len(data.get("data", ""))
                        print(f"🎵 Received AI audio response: {audio_length} bytes")
                except asyncio.TimeoutError:
                    pass  # No response yet, continue
            
            print("✅ Completed continuous audio streaming test")
            
            # End AI call
            end_call_message = {
                "type": "end_ai_call",
                "session": "continuous_test_session"
            }
            await websocket.send(json.dumps(end_call_message))
            print("📞 Ended AI call")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_continuous_audio_streaming())