# Voice Calling WebApp

A production-ready voice calling web application that provides real-time bidirectional audio communication with the AI backend.

## Features

- **Real-time Voice Communication**: Full-duplex audio streaming using WebSocket
- **Modern UI**: Clean, responsive interface built with Tailwind CSS
- **Cross-browser Support**: Compatible with Chrome, Firefox, and Edge
- **Audio Processing**: Web Audio API integration with 16-bit PCM conversion
- **Debug Console**: Real-time logging and monitoring
- **Error Handling**: Comprehensive error handling and user feedback
- **WebSocket Integration**: Seamless connection to existing backend on port 5010

## Architecture

```
webapp/
├── app.py                 # Flask server (port 1800)
├── templates/
│   └── index.html        # Main voice calling interface
├── static/
│   ├── js/
│   │   ├── websocket-manager.js    # WebSocket handling
│   │   ├── voice-call-engine.js    # Audio processing engine
│   │   └── ui-controller.js        # UI state management
│   └── css/
│       └── styles.css             # Custom styles
└── requirements.txt              # Python dependencies
```

## Quick Start

1. **Install Dependencies**:
   ```bash
   cd webapp
   python3 -m venv .venv
   source .venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Start the WebApp**:
   ```bash
   python3 app.py
   ```

3. **Access the Application**:
   Open your browser to `http://localhost:1800`

## Technical Details

### Audio Format
- **Input**: Browser microphone audio (variable sample rate)
- **Processing**: Downsampled to 8 kHz, converted to 16-bit PCM mono
- **Output**: Real-time playback of received audio from backend

### WebSocket Protocol
The application uses the existing backend message protocol:

```javascript
// User registration
{
  "type": "store_user",
  "session": "webapp_session_id",
  "data": {
    "name": "WebApp User",
    "mobile": "webapp",
    "userId": "webapp_session_id",
    "sessionType": "call"
  }
}

// Audio streaming
{
  "type": "audio_chunk",
  "session": "webapp_session_id",
  "data": [/* 16-bit PCM audio data array */]
}

// Call control
{
  "type": "start_ai_call",
  "session": "webapp_session_id"
}
```

### Browser Compatibility

| Feature | Chrome | Firefox | Edge | Safari |
|---------|--------|---------|------|--------|
| WebSocket | ✅ | ✅ | ✅ | ✅ |
| Web Audio API | ✅ | ✅ | ✅ | ✅ |
| getUserMedia | ✅ | ✅ | ✅ | ⚠️ |

**Note**: Safari requires HTTPS for microphone access in production.

## API Endpoints

### Flask Routes
- `GET /` - Serve main interface
- `POST /start-call` - Handle call start request
- `POST /end-call` - Handle call end request
- `GET /health` - Health check endpoint

### WebSocket Connection
- **Backend**: `ws://localhost:5010`
- **Protocol**: JSON messages over WebSocket
- **Authentication**: Session-based user management

## Security Features

- **CSP Compliance**: Content Security Policy compatible code
- **XSS Protection**: Safe DOM manipulation
- **Input Validation**: Sanitized user inputs
- **Secure WebSocket**: Error handling for connection failures

## Development

### File Structure
```
static/js/
├── websocket-manager.js    # WebSocket connection handling
├── voice-call-engine.js    # Audio capture and playback
└── ui-controller.js        # User interface management
```

### Key Classes

#### `WebSocketManager`
- Manages WebSocket connection to backend
- Handles message routing and error recovery
- Provides statistics and monitoring

#### `VoiceCallEngine`
- Web Audio API integration
- Real-time audio processing
- Format conversion (PCM 16-bit, 8 kHz)

#### `UIController`
- Interface state management
- Debug console functionality
- User feedback and error handling

## Configuration

### Environment Variables
- `FLASK_ENV`: Development/production mode
- `WEBSOCKET_URL`: Backend WebSocket URL (default: ws://localhost:5010)

### Audio Settings
```javascript
// Configurable in voice-call-engine.js
const SAMPLE_RATE = 8000;    // Target sample rate
const BUFFER_SIZE = 4096;    // Audio buffer size
const BIT_DEPTH = 16;        // PCM bit depth
```

## Troubleshooting

### Common Issues

1. **Microphone Permission Denied**
   - Ensure HTTPS in production
   - Check browser permissions
   - Restart browser if needed

2. **WebSocket Connection Failed**
   - Verify backend is running on port 5010
   - Check firewall settings
   - Ensure WebSocket server is accessible

3. **Audio Quality Issues**
   - Check microphone settings
   - Verify sample rate conversion
   - Monitor debug console for errors

### Debug Console
The application includes a comprehensive debug console that logs:
- WebSocket connection events
- Audio processing statistics
- Error messages and warnings
- Performance metrics

## Performance

### Optimizations
- Efficient audio buffer management
- Minimal DOM manipulation
- Lazy loading of audio contexts
- Connection pooling for WebSocket

### Monitoring
- Real-time packet statistics
- Audio level monitoring
- Connection status tracking
- Error rate monitoring

## Production Deployment

1. **HTTPS Required**: For microphone access in production
2. **WebSocket Security**: Use WSS in production
3. **Load Balancing**: Consider sticky sessions for WebSocket
4. **Monitoring**: Implement error tracking and analytics

## License

This voice calling webapp integrates with the existing AI voice mate backend system.