SHA256:07jcXlJ4SkBnyTmaVnmTpXuBiRx2+Q2adxbttO9gt0M.

ngrok config add-authtoken *************************************************

ngrok http http://localhost:3010

development
ngrok http --domain=testpreset.ngrok.app http://localhost:3011

production
ngrok http --domain=preset.ngrok.app http://localhost:3010

ongo faq api
project file-path
C:\Users\<USER>\PycharmProjects\ongo_faq_api
http://localhost:3020/best_match

option 1
docker run -it -p 5002:5002 --gpus all --name coqui-tts --entrypoint "" ghcr.io/coqui-ai/tts python3 TTS/server/server.py --model_name tts_models/en/vctk/vits --use_cuda true
option 2
docker run -it -p 5002:5002 --gpus all --name coqui-tts --entrypoint /bin/bash ghcr.io/coqui-ai/tts
python3 TTS/server/server.py --list_models #To get the list of available models
python3 TTS/server/server.py --model_name tts_models/en/vctk/vits --use_cuda true
p263 good nice nice nice
p264 good nice nice nice

p225: Clear and natural male voice.
p226: Warm tone, suitable for instructional content.
p227: Professional-sounding voice.
p229: Friendly and engaging.
p230: Articulate and clear pronunciation.
p248: Neutral accent, good for learners.
p270: Calm and steady voice. ***
p273: Expressive and engaging.
p303: Clear enunciation, suitable for teaching.

male voice ids

p226: Warm and friendly tone.
p229: Professional and engaging.
p230: Neutral accent, good for learners.
p228 - Clear and neutral, often used in standard TTS models.
p257 - Male voice from the VCTK dataset, well-balanced and neutral.
p257 – Male, slightly deeper voice, clear, and neutral.


female voice ids
p345: Good articulation, clear speech.
p248: Neutral accent, good for learners. // very nice voice
p237 good
p240
p243 good
p244 good
p245
p246
p247
p248 Neutral accent, good for learners.
p249
p250
p259 good
p260 good good
p261
p263 good nice nice nice
p268
p270 good nice
p271 good
p273 good nice nice
p274
p275 good
p276
p277 good nice
p278 good nice
p280 good
p283 good nice
p284
p288
p293
p294
p295
p297 good
p300 good nice nice
p303 good nice nice nice nice
p304
p305
p306 good nice nice
p308
p310
p311
p314 good nice
p316 good nice
p323 good nice
p329
p333 good
p334
p335 good nice nice
p336 good
p339 good
p341 good nice nice
p343 good
p345 good nice nice nice nice
p347 good nice
p360 good
p361 good
p362 good
p363 good
p364 good nice nice nice
p374 good


=============

Step 1: Find Your WSL IP Address
Run the following command inside your WSL terminal to find the IP address of WSL:

bash
Copy code
ip addr show eth0

Port Forwarding Using netsh
Sometimes, you may need to forward the WSL port to Windows. You can use the netsh command to forward the port from WSL to the Windows host.

Step 1: Forward the Port
Open a Windows command prompt with Administrator privileges and run:

cmd
Copy code
netsh interface portproxy add v4tov4 listenaddress=0.0.0.0 listenport=4483 connectaddress=************ connectport=4483
netsh interface portproxy add v4tov4 listenaddress=0.0.0.0 listenport=4010 connectaddress=************ connectport=4010
netsh interface portproxy add v4tov4 listenaddress=0.0.0.0 listenport=5010 connectaddress=************ connectport=5010
netsh interface portproxy add v4tov4 listenaddress=0.0.0.0 listenport=44312 connectaddress=************ connectport=44312
netsh interface portproxy add v4tov4 listenaddress=0.0.0.0 listenport=8000 connectaddress=************ connectport=8000

Replace 4483 with the port your API is using, and replace <WSL-IP> with the WSL IP address you found earlier.

Step 2: Verify the Port Forwarding
To check that the port forwarding is correctly set up, run the following:

cmd
Copy code
netsh interface portproxy show all

---------
To remove the port forwarding rule that was added using netsh, you can use the following command:

cmd
Copy code
netsh interface portproxy delete v4tov4 listenaddress=0.0.0.0 listenport=4483

--------------------
Microsoft Windows [Version 10.0.22621.4037]
(c) Microsoft Corporation. All rights reserved.

C:\Windows\System32>netsh interface portproxy add v4tov4 listenaddress=0.0.0.0 listenport=4483 connectaddress=************ connectport=4483


C:\Windows\System32>netsh interface portproxy show all

Listen on ipv4:             Connect to ipv4:

Address         Port        Address         Port
--------------- ----------  --------------- ----------
0.0.0.0         5000        ************    5000
0.0.0.0         3010        ************    3010
0.0.0.0         4483        ************    4483


C:\Windows\System32>
