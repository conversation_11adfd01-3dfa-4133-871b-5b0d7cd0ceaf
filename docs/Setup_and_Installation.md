# Setup and Installation Guide

This comprehensive guide will help you set up and deploy the AI Voice Mate system for development and production environments.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 18.04+), macOS, or Windows 10+
- **Python**: 3.8 or higher
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: At least 10GB free space
- **Network**: Internet connection for API dependencies

### Required Software
- **Git**: For cloning the repository
- **Python 3.8+**: Core runtime environment
- **pip**: Python package manager
- **Virtual Environment**: `venv` or `virtualenv`
- **FFmpeg**: For audio processing (optional but recommended)

### Optional Dependencies
- **Docker**: For containerized deployment
- **Nginx**: For production reverse proxy
- **PostgreSQL/MySQL**: For production database (SQLite used by default)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/YourUsername/ai-voice-mate.git
cd ai-voice-mate
```

### 2. Create Virtual Environment

```bash
# Create virtual environment
python3 -m venv .venv

# Activate virtual environment
# On Linux/macOS:
source .venv/bin/activate

# On Windows:
.venv\Scripts\activate
```

### 3. Install Python Dependencies

```bash
# Upgrade pip
pip install --upgrade pip

# Install required packages
pip install -r requirements.txt
```

### 4. Install System Dependencies

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install -y ffmpeg portaudio19-dev python3-pyaudio
```

#### macOS:
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install ffmpeg portaudio
```

#### Windows:
```bash
# Install FFmpeg (download from https://ffmpeg.org/download.html)
# Add FFmpeg to your system PATH

# Install PyAudio
pip install pipwin
pipwin install pyaudio
```

### 5. Environment Configuration

Create a `.env` file in the project root:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# User Information
TITLE="Sir"
NAME="Your Name"

# Gmail Credentials (for notifications)
GMAIL_USER="<EMAIL>"
GMAIL_PASS="your_gmail_app_password"
RECIPIENT="<EMAIL>"

# Phone Number for SMS (optional)
PHONE_NUMBER="<EMAIL>"

# API Configuration
API_HOST="0.0.0.0"
API_PORT="5012"
WEBSOCKET_PORT="5010"

# Speech Synthesis
SPEECH_SYNTHESIS_TIMEOUT=10

# Database Configuration (optional)
DATABASE_URL="sqlite:///./ai_voice_mate.db"

# Security (for production)
BOT_SECRET="your_secure_secret_key"

# OpenAI API (if using OpenAI models)
OPENAI_API_KEY="your_openai_api_key"
```

### 6. Database Setup

Initialize the database:

```bash
# Run database migrations (if applicable)
python -m tutor.modules.database.database

# Or start the application (it will create tables automatically)
python main.py
```

### 7. Audio Storage Setup

Create necessary directories:

```bash
mkdir -p fileio/sessions
mkdir -p logs
mkdir -p audio_storage
```

## Running the Application

### Development Mode

#### Start the Main Application:
```bash
python main.py
```

#### Start the API Server:
```bash
python -m tutor.api.main
```

#### Start the WebSocket Server:
```bash
python -m tutor.websocket_server
```

#### Start the Web Interface (optional):
```bash
python -m tutor.app
```

### Production Mode

#### Using Gunicorn (Recommended):

```bash
# Install Gunicorn
pip install gunicorn

# Start API server
gunicorn -w 4 -k uvicorn.workers.UvicornWorker tutor.api.main:app --bind 0.0.0.0:5012

# Start WebSocket server in background
nohup python -m tutor.websocket_server &
```

#### Using Docker:

```bash
# Build Docker image
docker build -t ai-voice-mate .

# Run container
docker run -d \
  --name ai-voice-mate \
  -p 5012:5012 \
  -p 5010:5010 \
  -v $(pwd)/fileio:/app/fileio \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  ai-voice-mate
```

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `API_HOST` | API server host | `0.0.0.0` |
| `API_PORT` | API server port | `5012` |
| `WEBSOCKET_PORT` | WebSocket server port | `5010` |
| `SPEECH_SYNTHESIS_TIMEOUT` | TTS timeout in seconds | `10` |
| `DATABASE_URL` | Database connection string | SQLite default |
| `OPENAI_API_KEY` | OpenAI API key | None |
| `BOT_SECRET` | Security secret for webhooks | None |

### Audio Configuration

Edit `tutor/modules/models/models.py` for audio settings:

```python
# Audio processing settings
SAMPLE_RATE = 16000
CHANNELS = 1
CHUNK_SIZE = 1024
AUDIO_FORMAT = "wav"
```

### Logging Configuration

Logs are stored in the `logs/` directory:
- `agsvoice_api_*.log`: API server logs
- `background_tasks_*.log`: Background process logs
- `qa_report_*.log`: QA analysis logs

## Verification

### 1. Test API Endpoints

```bash
# Test health check
curl http://localhost:5012/

# Test TTS endpoint
curl -X POST "http://localhost:5012/api/tts" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello world"}' \
  --output test.wav
```

### 2. Test WebSocket Connection

```bash
# Install wscat for testing
npm install -g wscat

# Test WebSocket connection
wscat -c ws://localhost:5010
```

### 3. Test Voice Call Integration

Use the provided client examples in the [Voice Call Integration](./Voice_Call_Integration.md) documentation.

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Find process using port
lsof -i :5012
lsof -i :5010

# Kill process
kill -9 <PID>
```

#### 2. Audio Dependencies Missing
```bash
# Ubuntu/Debian
sudo apt install -y portaudio19-dev python3-pyaudio

# Reinstall PyAudio
pip uninstall pyaudio
pip install pyaudio
```

#### 3. Permission Errors
```bash
# Fix file permissions
chmod +x install.sh
chmod -R 755 fileio/
chmod -R 755 logs/
```

#### 4. Database Connection Issues
```bash
# Reset database
rm -f ai_voice_mate.db
python -m tutor.modules.database.database
```

### Performance Optimization

#### 1. Audio Processing
- Use dedicated audio processing server for high-load scenarios
- Implement audio compression for network efficiency
- Configure appropriate buffer sizes

#### 2. Database Optimization
- Use PostgreSQL or MySQL for production
- Implement connection pooling
- Add database indexes for frequently queried fields

#### 3. Caching
- Implement Redis for session caching
- Cache TTS responses for common phrases
- Use CDN for static audio files

## Security Considerations

### Production Deployment

1. **Use HTTPS/WSS**: Configure SSL certificates
2. **Environment Variables**: Never commit sensitive data
3. **Firewall**: Restrict access to necessary ports only
4. **Authentication**: Implement proper API authentication
5. **Rate Limiting**: Add rate limiting for API endpoints
6. **Input Validation**: Validate all user inputs
7. **Logging**: Monitor and log security events

### Recommended Security Headers

```nginx
# Nginx configuration
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

## Next Steps

1. Review the [Architecture Overview](./Architecture_Overview.md) to understand system components
2. Check the [API Reference](./API_Reference.md) for available endpoints
3. Follow the [Frontend Integration Guide](./Frontend_Integration_Guide.md) for UI development
4. Implement [Authentication and Security](./Authentication_and_Security.md) measures

## Support

For additional help:
- Check the [Troubleshooting](./Troubleshooting.md) guide
- Review [Error Handling](./Error_Handling.md) documentation
- Examine log files in the `logs/` directory
