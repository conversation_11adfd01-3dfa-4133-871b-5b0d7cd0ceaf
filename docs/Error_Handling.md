# Error Handling

This document provides comprehensive information about error handling, troubleshooting, and debugging in the AI Voice Mate system.

## Error Response Format

All API endpoints follow a consistent error response format:

```json
{
  "status": "error",
  "message": "Description of the error",
  "error_code": "ERROR_CODE_IDENTIFIER",
  "details": {
    "field": "Additional error details",
    "timestamp": "2023-06-01T12:00:00Z"
  }
}
```

## HTTP Status Codes

### Success Codes
- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **204 No Content**: Request successful, no content to return

### Client Error Codes
- **400 Bad Request**: Invalid request format or parameters
- **401 Unauthorized**: Authentication required or failed
- **403 Forbidden**: Access denied
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource already exists
- **422 Unprocessable Entity**: Validation error
- **429 Too Many Requests**: Rate limit exceeded

### Server Error Codes
- **500 Internal Server Error**: Unexpected server error
- **502 Bad Gateway**: Upstream service error
- **503 Service Unavailable**: Service temporarily unavailable
- **504 Gateway Timeout**: Upstream service timeout

## API Error Codes

### Authentication Errors

#### AUTH_001: Invalid API Key
```json
{
  "status": "error",
  "message": "Invalid API key provided",
  "error_code": "AUTH_001"
}
```

#### AUTH_002: Token Expired
```json
{
  "status": "error",
  "message": "Authentication token has expired",
  "error_code": "AUTH_002"
}
```

#### AUTH_003: Insufficient Permissions
```json
{
  "status": "error",
  "message": "Insufficient permissions to access this resource",
  "error_code": "AUTH_003"
}
```

### Validation Errors

#### VAL_001: Missing Required Field
```json
{
  "status": "error",
  "message": "Missing required field: 'call_id'",
  "error_code": "VAL_001",
  "details": {
    "field": "call_id",
    "required": true
  }
}
```

#### VAL_002: Invalid Field Format
```json
{
  "status": "error",
  "message": "Invalid format for field 'mobile'",
  "error_code": "VAL_002",
  "details": {
    "field": "mobile",
    "expected_format": "E.164 international format"
  }
}
```

#### VAL_003: Field Value Out of Range
```json
{
  "status": "error",
  "message": "Field value exceeds maximum length",
  "error_code": "VAL_003",
  "details": {
    "field": "message",
    "max_length": 1000,
    "provided_length": 1500
  }
}
```

### File Upload Errors

#### FILE_001: Invalid File Type
```json
{
  "status": "error",
  "message": "Uploaded file must be an audio file",
  "error_code": "FILE_001",
  "details": {
    "allowed_types": ["audio/wav", "audio/mpeg", "audio/ogg"],
    "provided_type": "text/plain"
  }
}
```

#### FILE_002: File Too Large
```json
{
  "status": "error",
  "message": "File size exceeds maximum limit",
  "error_code": "FILE_002",
  "details": {
    "max_size_mb": 50,
    "provided_size_mb": 75
  }
}
```

#### FILE_003: File Already Exists
```json
{
  "status": "error",
  "message": "Audio file already exists for this call_id",
  "error_code": "FILE_003",
  "details": {
    "call_id": "12345",
    "existing_file": "/path/to/existing/file.wav",
    "suggestion": "Use overwrite=true to replace existing file"
  }
}
```

### Processing Errors

#### PROC_001: TTS Generation Failed
```json
{
  "status": "error",
  "message": "Failed to generate audio file",
  "error_code": "PROC_001",
  "details": {
    "service": "text-to-speech",
    "reason": "Service temporarily unavailable"
  }
}
```

#### PROC_002: Audio Processing Failed
```json
{
  "status": "error",
  "message": "Failed to process audio file",
  "error_code": "PROC_002",
  "details": {
    "file_path": "/path/to/audio.wav",
    "reason": "Unsupported audio format"
  }
}
```

#### PROC_003: Database Operation Failed
```json
{
  "status": "error",
  "message": "Database operation failed",
  "error_code": "PROC_003",
  "details": {
    "operation": "INSERT",
    "table": "qa_reports",
    "reason": "Connection timeout"
  }
}
```

## WebSocket Error Messages

### Connection Errors

#### WS_001: Invalid Session
```json
{
  "type": "error",
  "error_code": "WS_001",
  "message": "Invalid session ID provided"
}
```

#### WS_002: Session Expired
```json
{
  "type": "error",
  "error_code": "WS_002",
  "message": "Session has expired, please reconnect"
}
```

#### WS_003: User Not Found
```json
{
  "type": "error",
  "error_code": "WS_003",
  "message": "User not found for session"
}
```

### Message Format Errors

#### WS_004: Invalid Message Format
```json
{
  "type": "error",
  "error_code": "WS_004",
  "message": "Invalid message format",
  "details": {
    "expected_fields": ["type", "session", "data"],
    "missing_fields": ["session"]
  }
}
```

#### WS_005: Unsupported Message Type
```json
{
  "type": "error",
  "error_code": "WS_005",
  "message": "Unsupported message type",
  "details": {
    "provided_type": "invalid_type",
    "supported_types": ["store_user", "start_ai_call", "audio_chunk"]
  }
}
```

### Audio Processing Errors

#### WS_006: Audio Processing Failed
```json
{
  "type": "error",
  "error_code": "WS_006",
  "message": "Failed to process audio chunk",
  "details": {
    "chunk_size": 1024,
    "reason": "Invalid audio format"
  }
}
```

#### WS_007: Speech Recognition Failed
```json
{
  "type": "error",
  "error_code": "WS_007",
  "message": "Speech recognition service unavailable",
  "details": {
    "service": "speech-to-text",
    "retry_after": 30
  }
}
```

## Common Issues and Solutions

### 1. Connection Issues

#### Problem: Cannot connect to WebSocket server
**Symptoms:**
- Connection refused errors
- Timeout errors
- WebSocket handshake failures

**Solutions:**
```bash
# Check if WebSocket server is running
netstat -tlnp | grep :5010

# Check server logs
tail -f logs/websocket_server.log

# Restart WebSocket server
python -m tutor.websocket_server
```

#### Problem: API server not responding
**Symptoms:**
- HTTP 502/503 errors
- Connection timeouts
- No response from API endpoints

**Solutions:**
```bash
# Check if API server is running
netstat -tlnp | grep :5012

# Check server logs
tail -f logs/agsvoice_api_*.log

# Restart API server
python -m tutor.api.main
```

### 2. Audio Processing Issues

#### Problem: TTS generation fails
**Symptoms:**
- HTTP 500 errors on `/api/tts`
- "Failed to generate audio file" messages

**Solutions:**
```python
# Check TTS service configuration
import tutor.modules.audio.text_speech as tts
tts.test_tts_service()

# Verify audio dependencies
pip install --upgrade pydub speechsynthesis

# Check system audio libraries
sudo apt-get install espeak espeak-data libespeak-dev
```

#### Problem: Audio file upload fails
**Symptoms:**
- File type validation errors
- File size limit exceeded
- Permission denied errors

**Solutions:**
```bash
# Check file permissions
chmod 755 audio_storage/
chmod 644 audio_storage/api_upload/

# Verify file format
file uploaded_audio.wav
ffprobe uploaded_audio.wav

# Check available disk space
df -h
```

### 3. Database Issues

#### Problem: Database connection errors
**Symptoms:**
- "Database operation failed" errors
- Connection timeout messages
- SQLite database locked errors

**Solutions:**
```python
# Check database file permissions
import os
db_path = "ai_voice_mate.db"
print(f"Database exists: {os.path.exists(db_path)}")
print(f"Database readable: {os.access(db_path, os.R_OK)}")
print(f"Database writable: {os.access(db_path, os.W_OK)}")

# Reset database (caution: data loss)
import tutor.modules.database.database as db
db.reset_database()
```

#### Problem: QA results not found
**Symptoms:**
- Empty results from `/api/qa-results`
- "No data found" messages

**Solutions:**
```sql
-- Check database contents
SELECT COUNT(*) FROM qa_reports;
SELECT * FROM qa_reports LIMIT 5;

-- Verify table structure
.schema qa_reports
```

### 4. Performance Issues

#### Problem: High memory usage
**Symptoms:**
- System slowdown
- Out of memory errors
- Process crashes

**Solutions:**
```bash
# Monitor memory usage
top -p $(pgrep -f "python.*tutor")
htop

# Check for memory leaks
python -m memory_profiler main.py

# Optimize audio processing
# Reduce chunk sizes in audio processing
# Implement audio compression
```

#### Problem: High CPU usage
**Symptoms:**
- System lag
- Slow response times
- High load averages

**Solutions:**
```bash
# Monitor CPU usage
top -p $(pgrep -f "python.*tutor")

# Profile CPU usage
python -m cProfile -o profile.stats main.py

# Optimize processing
# Implement async processing
# Use connection pooling
# Add caching layers
```

## Debugging Tools

### 1. Logging Configuration

#### Enable Debug Logging:
```python
import logging

# Set debug level for all loggers
logging.basicConfig(level=logging.DEBUG)

# Enable specific module debugging
logger = logging.getLogger('tutor.websocket_server')
logger.setLevel(logging.DEBUG)
```

#### Custom Log Formatter:
```python
import logging

formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
)

handler = logging.StreamHandler()
handler.setFormatter(formatter)

logger = logging.getLogger('tutor')
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)
```

### 2. Health Check Endpoints

#### API Health Check:
```bash
curl http://localhost:5012/health
```

#### WebSocket Health Check:
```javascript
const ws = new WebSocket('ws://localhost:5010');
ws.onopen = () => {
    console.log('WebSocket connection established');
    ws.send(JSON.stringify({
        type: 'health_check',
        session: 'test_session',
        data: null
    }));
};
```

### 3. Testing Tools

#### API Testing:
```bash
# Test TTS endpoint
curl -X POST "http://localhost:5012/api/tts" \
  -H "Content-Type: application/json" \
  -d '{"message": "Test message"}' \
  --output test_output.wav

# Test QA results
curl "http://localhost:5012/api/qa-results?limit=1"
```

#### WebSocket Testing:
```bash
# Install wscat
npm install -g wscat

# Test WebSocket connection
wscat -c ws://localhost:5010

# Send test message
{"type": "store_user", "session": "test_123", "data": {"name": "Test User"}}
```

## Monitoring and Alerting

### 1. Log Monitoring

#### Key Log Files:
- `logs/agsvoice_api_*.log`: API server logs
- `logs/background_tasks_*.log`: Background process logs
- `logs/qa_report_*.log`: QA analysis logs
- `logs/websocket_server.log`: WebSocket server logs

#### Log Analysis:
```bash
# Monitor error patterns
grep -i "error" logs/*.log | tail -20

# Monitor performance issues
grep -i "timeout\|slow\|performance" logs/*.log

# Monitor security events
grep -i "auth\|security\|unauthorized" logs/*.log
```

### 2. System Monitoring

#### Resource Monitoring:
```bash
# Monitor system resources
watch -n 1 'ps aux | grep python | grep tutor'

# Monitor network connections
watch -n 1 'netstat -tlnp | grep -E "(5010|5012)"'

# Monitor disk usage
watch -n 5 'df -h'
```

## Recovery Procedures

### 1. Service Recovery

#### Restart All Services:
```bash
#!/bin/bash
# restart_services.sh

echo "Stopping services..."
pkill -f "python.*tutor"

echo "Starting WebSocket server..."
nohup python -m tutor.websocket_server > logs/websocket.log 2>&1 &

echo "Starting API server..."
nohup python -m tutor.api.main > logs/api.log 2>&1 &

echo "Services restarted successfully"
```

### 2. Database Recovery

#### Backup and Restore:
```bash
# Create backup
cp ai_voice_mate.db ai_voice_mate.db.backup.$(date +%Y%m%d_%H%M%S)

# Restore from backup
cp ai_voice_mate.db.backup.20230601_120000 ai_voice_mate.db
```

## Next Steps

1. Review [Setup and Installation](./Setup_and_Installation.md) for proper configuration
2. Check [Authentication and Security](./Authentication_and_Security.md) for security-related errors
3. Follow [Frontend Integration Guide](./Frontend_Integration_Guide.md) for client-side error handling
4. Implement monitoring and alerting based on your infrastructure
