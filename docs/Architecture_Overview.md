# Architecture Overview

This document provides a comprehensive overview of the AI Voice Mate system architecture, including its components, data flow, and integration patterns.

## System Overview

AI Voice Mate is a multi-component system designed for real-time voice communication with AI tutoring agents. The architecture follows a microservices pattern with clear separation of concerns.

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Mobile App    │    │   Third-party   │
│   (Web/React)   │    │   (iOS/Android) │    │   Integrations  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Load Balancer        │
                    │      (Nginx/HAProxy)      │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────▼─────┐         ┌───────▼───────┐       ┌───────▼───────┐
    │ WebSocket │         │   REST API    │       │  Web Interface│
    │  Server   │         │    Server     │       │    Server     │
    │ (Port 5010)│         │ (Port 5012)   │       │ (Port 5000)   │
    └─────┬─────┘         └───────┬───────┘       └───────┬───────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                    ┌─────────────▼─────────────┐
                    │     Core Application      │
                    │      (AI Voice Mate)      │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────▼─────┐         ┌───────▼───────┐       ┌───────▼───────┐
    │  Audio    │         │   Database    │       │   External    │
    │ Processing│         │   (SQLite/    │       │   Services    │
    │  Engine   │         │ PostgreSQL)   │       │ (OpenAI, etc) │
    └───────────┘         └───────────────┘       └───────────────┘
```

## Core Components

### 1. WebSocket Server (`tutor/websocket_server.py`)
- **Purpose**: Real-time voice communication
- **Port**: 5010
- **Responsibilities**:
  - Handle WebSocket connections
  - Manage user sessions
  - Process audio chunks
  - Coordinate with AI processing
  - Real-time transcription

### 2. REST API Server (`tutor/api/main.py`)
- **Purpose**: HTTP-based API services
- **Port**: 5012
- **Responsibilities**:
  - Text-to-Speech conversion
  - QA results retrieval
  - Audio file management
  - File upload handling

### 3. Web Interface Server (`tutor/app.py`)
- **Purpose**: Administrative web interface
- **Port**: 5000
- **Responsibilities**:
  - User management dashboard
  - Real-time monitoring
  - System status display

### 4. Core Application (`main.py`)
- **Purpose**: Main application orchestrator
- **Responsibilities**:
  - Initialize all services
  - Coordinate between components
  - Handle system-wide configuration

## Data Flow Architecture

### Voice Call Flow

```
1. Client Connection
   ┌─────────┐    WebSocket     ┌─────────────┐
   │ Client  │ ──────────────► │ WebSocket   │
   │         │                 │ Server      │
   └─────────┘                 └─────────────┘

2. User Registration
   ┌─────────┐   store_user    ┌─────────────┐   Session   ┌─────────────┐
   │ Client  │ ──────────────► │ WebSocket   │ ──────────► │ Session     │
   │         │                 │ Server      │             │ Manager     │
   └─────────┘                 └─────────────┘             └─────────────┘

3. Audio Processing
   ┌─────────┐  audio_chunk    ┌─────────────┐   Process   ┌─────────────┐
   │ Client  │ ──────────────► │ WebSocket   │ ──────────► │ Audio       │
   │         │                 │ Server      │             │ Processor   │
   └─────────┘                 └─────────────┘             └─────────────┘
                                       │                           │
                                       ▼                           ▼
                               ┌─────────────┐             ┌─────────────┐
                               │ AI Engine   │             │ Speech-to-  │
                               │ (OpenAI)    │             │ Text Engine │
                               └─────────────┘             └─────────────┘

4. Response Generation
   ┌─────────────┐   AI Text   ┌─────────────┐   TTS       ┌─────────────┐
   │ AI Engine   │ ──────────► │ WebSocket   │ ──────────► │ Text-to-    │
   │             │             │ Server      │             │ Speech      │
   └─────────────┘             └─────────────┘             └─────────────┘
                                       │                           │
                                       ▼                           ▼
                               ┌─────────────┐             ┌─────────────┐
                               │ Client      │ ◄─────────── │ Audio       │
                               │             │ llm_answer   │ Chunks      │
                               └─────────────┘             └─────────────┘
```

### API Request Flow

```
1. HTTP Request
   ┌─────────┐    HTTP/REST    ┌─────────────┐
   │ Client  │ ──────────────► │ API Server  │
   │         │                 │ (FastAPI)   │
   └─────────┘                 └─────────────┘

2. Request Processing
   ┌─────────────┐   Route     ┌─────────────┐   Business  ┌─────────────┐
   │ API Server  │ ──────────► │ Router      │ ──────────► │ Logic       │
   │             │             │ Handler     │             │ Module      │
   └─────────────┘             └─────────────┘             └─────────────┘

3. Data Access
   ┌─────────────┐   Query     ┌─────────────┐   SQL       ┌─────────────┐
   │ Business    │ ──────────► │ Database    │ ──────────► │ SQLite/     │
   │ Logic       │             │ Layer       │             │ PostgreSQL  │
   └─────────────┘             └─────────────┘             └─────────────┘

4. Response
   ┌─────────────┐   JSON/     ┌─────────────┐   HTTP      ┌─────────────┐
   │ Database    │   Audio     │ API Server  │ ──────────► │ Client      │
   │ Layer       │ ──────────► │             │             │             │
   └─────────────┘             └─────────────┘             └─────────────┘
```

## Module Architecture

### Audio Processing Module (`tutor/modules/audio/`)
```
audio/
├── audio_processor.py      # Main audio processing logic
├── listener.py            # Audio input handling
├── text_speech.py         # Text-to-speech conversion
└── synthesize_speech.py   # Speech synthesis utilities
```

### Assistant Modules (`tutor/modules/assistants/`)
```
assistants/
├── voice_recorder_assistant.py  # Voice recording functionality
├── qa_assistant.py             # Quality assurance analysis
├── bank_assistant.py           # Banking conversation scenarios
└── sentiment_analyze_assistant.py  # Sentiment analysis
```

### Database Module (`tutor/modules/database/`)
```
database/
├── database.py            # Database connection and operations
└── msql_database.py       # MySQL-specific implementations
```

### API Routers (`tutor/api/routers/`)
```
routers/
└── english_bot.py         # Main API endpoints
```

## Data Models

### User Session
```python
{
    "session_id": "unique_session_identifier",
    "user_data": {
        "name": "User Name",
        "mobile": "+**********",
        "userId": "user_001",
        "sessionType": "call|chat",
        "target": "english_tutor"
    },
    "conversation_history": [],
    "audio_processor": AudioProcessor,
    "websocket": WebSocketConnection
}
```

### QA Report
```python
{
    "report_id": "QA2023060112345",
    "call_id": "12345",
    "call_summary": "Summary text",
    "total_score": 85,
    "call_category": "Good|Average|Poor",
    "timestamp": 1685610000,
    "agent_strengths": ["strength1", "strength2"],
    "agent_areas_for_improvement": ["area1", "area2"],
    "parameter_scores": [
        {
            "parameter_id": 101,
            "score": 1.5,
            "comments": "Detailed feedback"
        }
    ]
}
```

### Audio Chunk
```python
{
    "type": "audio_chunk",
    "session": "session_id",
    "data": [255, 128, 64, 32, ...]  # Byte array
}
```

## Integration Points

### External Services
1. **OpenAI API**: AI conversation generation
2. **Speech-to-Text Services**: Audio transcription
3. **Text-to-Speech Services**: Audio synthesis
4. **Database Systems**: Data persistence

### Internal Communication
1. **WebSocket Messages**: Real-time communication
2. **HTTP APIs**: Request-response patterns
3. **File System**: Audio file storage
4. **Database Queries**: Data operations

## Scalability Considerations

### Horizontal Scaling
- **Load Balancing**: Distribute WebSocket connections
- **Database Sharding**: Partition data by user/session
- **Microservices**: Split components into separate services

### Performance Optimization
- **Connection Pooling**: Database connections
- **Caching**: Redis for session data
- **CDN**: Static audio file delivery
- **Compression**: Audio data optimization

### Monitoring and Observability
- **Logging**: Structured logging across all components
- **Metrics**: Performance and usage metrics
- **Health Checks**: Service availability monitoring
- **Alerting**: Automated issue detection

## Security Architecture

### Authentication Flow
```
1. Client Request
   ┌─────────┐   API Key/     ┌─────────────┐
   │ Client  │   Token        │ API Gateway │
   │         │ ──────────────► │             │
   └─────────┘                 └─────────────┘

2. Validation
   ┌─────────────┐   Validate  ┌─────────────┐
   │ API Gateway │ ──────────► │ Auth        │
   │             │             │ Service     │
   └─────────────┘             └─────────────┘

3. Authorization
   ┌─────────────┐   Authorize ┌─────────────┐
   │ Auth        │ ──────────► │ Business    │
   │ Service     │             │ Logic       │
   └─────────────┘             └─────────────┘
```

### Data Protection
- **Encryption**: TLS/SSL for data in transit
- **Input Validation**: Sanitize all user inputs
- **Rate Limiting**: Prevent abuse
- **Audit Logging**: Track security events

## Deployment Architecture

### Development Environment
```
┌─────────────────────────────────────────────┐
│              Local Machine                  │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│  │WebSocket│ │REST API │ │Web UI   │       │
│  │:5010    │ │:5012    │ │:5000    │       │
│  └─────────┘ └─────────┘ └─────────┘       │
│              SQLite Database                │
└─────────────────────────────────────────────┘
```

### Production Environment
```
┌─────────────────────────────────────────────┐
│              Load Balancer                  │
│                (Nginx)                      │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────┼───────────────────────────┐
│              App Server 1                   │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│  │WebSocket│ │REST API │ │Web UI   │       │
│  │:5010    │ │:5012    │ │:5000    │       │
│  └─────────┘ └─────────┘ └─────────┘       │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────┼───────────────────────────┐
│              App Server 2                   │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│  │WebSocket│ │REST API │ │Web UI   │       │
│  │:5010    │ │:5012    │ │:5000    │       │
│  └─────────┘ └─────────┘ └─────────┘       │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│           Shared Database                   │
│         (PostgreSQL/MySQL)                  │
└─────────────────────────────────────────────┘
```

## Next Steps

1. Review [API Reference](./API_Reference.md) for detailed endpoint documentation
2. Check [Voice Call Integration](./Voice_Call_Integration.md) for WebSocket implementation
3. Follow [Setup and Installation](./Setup_and_Installation.md) for deployment
4. Implement [Authentication and Security](./Authentication_and_Security.md) measures
