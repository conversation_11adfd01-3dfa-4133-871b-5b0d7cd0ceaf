# Voice Call Integration

This document provides comprehensive guidance for integrating voice call functionality with the AI Voice Mate system. The system uses WebSocket connections to handle real-time voice communication between clients and AI tutoring agents.

## Overview

The AI Voice Mate voice call system enables real-time audio communication with AI tutors through WebSocket connections. It supports:

- Real-time audio streaming
- Speech-to-text transcription
- AI-generated audio responses
- Session management
- Multiple conversation modes

## Server Configuration

### WebSocket Server
- **Host**: `0.0.0.0`
- **Port**: `5010`
- **Protocol**: WebSocket (ws://)
- **Connection URL**: `ws://localhost:5010`

### Audio Requirements
- **Sample Rate**: 16kHz (recommended)
- **Channels**: Mono (1 channel)
- **Format**: PCM 16-bit or WebM with Opus codec
- **Chunk Size**: 1024 bytes (recommended)

## Message Protocol

All WebSocket messages use JSON format with this structure:

```json
{
  "type": "message_type",
  "session": "unique_session_id",
  "data": "message_data"
}
```

## Message Types

### 1. User Registration
Register a user session before starting voice calls.

**Type**: `store_user`

```json
{
  "type": "store_user",
  "session": "user_session_123",
  "data": {
    "name": "John Doe",
    "mobile": "+1234567890",
    "userId": "user_001",
    "sentences": [],
    "sessionType": "call",
    "target": "english_tutor"
  }
}
```

**Response**:
```json
{
  "type": "store_user",
  "data": "user added successfully"
}
```

### 2. Start AI Call
Initiate an AI conversation session.

**Type**: `start_ai_call`

```json
{
  "type": "start_ai_call",
  "session": "user_session_123",
  "data": null
}
```

### 3. Enable AI Listening
Enable the AI to start listening for audio input.

**Type**: `ai_start_listening`

```json
{
  "type": "ai_start_listening",
  "session": "user_session_123",
  "data": null
}
```

### 4. Audio Streaming
Stream audio data in real-time.

**Type**: `audio_chunk`

```json
{
  "type": "audio_chunk",
  "session": "user_session_123",
  "data": [255, 128, 64, 32, ...]
}
```

**Note**: Audio data should be sent as an array of bytes.

### 5. Stop Voice Recording
Signal the end of user speech.

**Type**: `voice_action_stop`

```json
{
  "type": "voice_action_stop",
  "session": "user_session_123",
  "data": "stop_recording"
}
```

### 6. Text Input
Send text messages as an alternative to voice.

**Type**: `text_input`

```json
{
  "type": "text_input",
  "session": "user_session_123",
  "data": "Hello, I want to practice English"
}
```

### 7. End AI Call
Terminate the conversation session.

**Type**: `end_ai_call`

```json
{
  "type": "end_ai_call",
  "session": "user_session_123",
  "data": null
}
```

## Server Response Messages

### AI Text Response
```json
{
  "type": "ai_response",
  "data": "Hello! I'm your AI tutor. How can I help you today?"
}
```

### AI Audio Response
```json
{
  "type": "llm_answer",
  "data": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj..."
}
```

**Note**: Audio data is base64-encoded WAV format.

### Speech-to-Text Result
```json
{
  "type": "speech_text",
  "data": "Hello, I want to practice English"
}
```

### Real-time Transcription
```json
{
  "type": "transcript_batch",
  "data": "Hello, I want to practice English"
}
```

### Recording Control
```json
{
  "type": "pause_audio_recording",
  "data": ""
}
```

### Call End Notification
```json
{
  "type": "ai_end_call",
  "data": "ai call completed"
}
```

### Error Response
```json
{
  "type": "error",
  "message": "Error description"
}
```

## Session Types

- **chat**: Text-based conversation (600s disconnect delay)
- **call**: Voice-based conversation (5s disconnect delay)

## Audio Processing Pipeline

### Client to Server (Speech Input)
1. Client captures microphone input
2. Audio converted to byte array
3. Sent as `audio_chunk` messages
4. Server processes with speech-to-text
5. Server sends `transcript_batch` for real-time feedback

### Server to Client (AI Response)
1. AI generates text response
2. Server converts text to speech using TTS engine
3. Audio split into 512-byte chunks
4. Each chunk encoded as base64
5. Sent as `llm_answer` messages
6. Client decodes and plays audio chunks sequentially

## Complete Call Flow

### 1. Connection & Registration
```
Client -> Server: WebSocket Connection
Client -> Server: store_user message
Server -> Client: "user added successfully"
```

### 2. Start Voice Call
```
Client -> Server: start_ai_call
Client -> Server: ai_start_listening
Client -> Server: Continuous audio_chunk messages
```

### 3. AI Processing & Response
```
Server -> Client: transcript_batch (real-time transcription)
Server -> Client: pause_audio_recording (pause user recording)
Server -> Client: llm_answer (AI audio chunks - base64 encoded)
Server -> Client: speech_text (final transcribed user speech)
Server -> Client: ai_response (AI text response - fallback)
```

### 4. Ongoing Conversation
```
Client -> Server: audio_chunk (user speaking)
Server -> Client: transcript_batch (real-time transcription)
Client -> Server: voice_action_stop (user stops speaking)
Server -> Client: pause_audio_recording (pause recording)
Server -> Client: llm_answer (AI audio response chunks)
```

### 5. End Call
```
Client -> Server: end_ai_call
Server -> Client: ai_end_call
Connection closed
```

## Best Practices

### Connection Management
- Always register user before starting voice call
- Use unique session IDs for each connection
- Handle connection drops gracefully
- Implement automatic reconnection logic

### Audio Streaming
- Send audio chunks frequently (every 100ms)
- Stop recording when user stops speaking
- Handle microphone permissions properly
- Implement noise cancellation when possible

### Error Recovery
- Retry connections on failure
- Validate message format before sending
- Handle server errors gracefully
- Provide user feedback for issues

### Performance Optimization
- Use WebSocket compression when available
- Minimize audio chunk size for low latency
- Implement audio buffering for smooth playback
- Monitor connection quality

## Security Considerations

- Use secure WebSocket connections (WSS) in production
- Implement authentication and authorization
- Validate all incoming message formats
- Sanitize user inputs
- Implement rate limiting for audio streams
- Secure audio file storage and transmission

## Next Steps

- Review the [Frontend Integration Guide](./Frontend_Integration_Guide.md) for implementation examples
- Check [Error Handling](./Error_Handling.md) for troubleshooting
- See [API Reference](./API_Reference.md) for additional endpoints
