# API Reference

This document provides comprehensive documentation for all REST API endpoints available in the AI Voice Mate system.

## Base URL

```
http://localhost:5012
```

## Authentication

Currently, the API uses basic authentication mechanisms. For production deployments, refer to the [Authentication and Security](./Authentication_and_Security.md) guide.

## Content Types

- **Request**: `application/json` (for JSON endpoints), `multipart/form-data` (for file uploads)
- **Response**: `application/json`, `audio/wav`, `audio/mpeg`

## Endpoints Overview

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/tts` | POST | Text-to-Speech conversion |
| `/get-text-audio-file` | POST | Generate audio file from text |
| `/api/qa/update-audio-file` | POST | Upload audio file for QA analysis |
| `/api/qa-results` | GET | Retrieve QA analysis results |
| `/api/audio/{call_id}/{filename}` | GET | Serve audio files |

## Text-to-Speech API

### POST /api/tts

Convert text to speech and return audio file.

#### Request

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
  "message": "Hello, this is a test message for text-to-speech conversion."
}
```

#### Response

**Success (200 OK):**
- **Content-Type**: `audio/wav`
- **Body**: Binary audio data (WAV format)

**Error (400 Bad Request):**
```json
{
  "detail": "Bad Request"
}
```

**Error (422 Unprocessable Entity):**
```json
{
  "detail": "Unprocessable Entity"
}
```

**Error (500 Internal Server Error):**
```json
{
  "detail": "Failed to generate audio file"
}
```

#### Example

```bash
curl -X POST "http://localhost:5012/api/tts" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello world"}' \
  --output response.wav
```

## Audio File Generation

### POST /get-text-audio-file

Generate audio file from text with custom processing.

#### Request

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
  "message": "Text to convert to audio"
}
```

#### Response

**Success (200 OK):**
- **Content-Type**: `application/octet-stream`
- **Body**: Binary audio data
- **Filename**: `synthesized.wav`

**Error Responses**: Same as TTS endpoint

## Quality Assurance API

### POST /api/qa/update-audio-file

Upload audio file for quality assurance analysis.

#### Request

**Headers:**
```
Content-Type: multipart/form-data
```

**Form Data:**
- `call_id` (string, required): Unique identifier for the call
- `audio_file` (file, required): Audio file to upload
- `overwrite` (boolean, optional): Whether to overwrite existing file (default: false)

#### Response

**Success (200 OK):**
```json
{
  "status": "success",
  "message": "Audio file uploaded successfully. It will be processed by the transcription service.",
  "file_path": "/path/to/audio_storage/api_upload/12345/call_recording.wav",
  "call_id": "12345"
}
```

**Error (400 Bad Request):**
```json
{
  "status": "error",
  "message": "Uploaded file must be an audio file"
}
```

**Error (409 Conflict):**
```json
{
  "status": "error",
  "message": "Audio file already exists for this call_id. Use overwrite=true to replace it.",
  "file_path": "/path/to/existing/file.wav"
}
```

**Error (500 Internal Server Error):**
```json
{
  "status": "error",
  "message": "Failed to update audio file: [error details]"
}
```

#### Example

```bash
curl -X POST "http://localhost:5012/api/qa/update-audio-file" \
  -F "call_id=12345" \
  -F "audio_file=@recording.wav" \
  -F "overwrite=false"
```

### GET /api/qa-results

Retrieve quality assurance analysis results with optional filtering and detailed information.

#### Request

**Query Parameters:**

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `call_id` | string | No | - | Filter by specific call ID |
| `report_id` | string | No | - | Filter by specific QA report ID |
| `limit` | integer | No | 10 | Maximum number of results |
| `offset` | integer | No | 0 | Number of results to skip |
| `include_parameter_details` | boolean | No | false | Include full parameter definitions |
| `include_transcript` | boolean | No | false | Include transcript text and utterances |
| `include_audio` | boolean | No | false | Include audio file URLs |

#### Response

**Success (200 OK):**
```json
{
  "status": "success",
  "data": [
    {
      "report_id": "***************",
      "call_summary": "Customer called regarding billing issue with their account.",
      "total_score": 85,
      "call_category": "Good",
      "timestamp": **********,
      "agent_strengths": [
        "Clear communication",
        "Empathy",
        "Problem resolution"
      ],
      "agent_areas_for_improvement": [
        "Call opening",
        "Documentation"
      ],
      "call_id": "12345",
      "parameter_scores": [
        {
          "parameter_id": 101,
          "score": 1.5,
          "comments": "Agent greeted customer professionally but didn't introduce themselves."
        }
      ]
    }
  ],
  "count": 1
}
```

**With Audio Data (`include_audio=true`):**
```json
{
  "status": "success",
  "data": [
    {
      "report_id": "***************",
      "call_summary": "Customer called regarding billing issue.",
      "total_score": 85,
      "call_category": "Good",
      "timestamp": **********,
      "agent_strengths": ["Clear communication"],
      "agent_areas_for_improvement": ["Call opening"],
      "call_id": "12345",
      "audio": {
        "url": "/api/audio/12345/call_recording.wav",
        "file_path": "/path/to/audio_storage/api_upload/12345/call_recording.wav",
        "duration": null
      },
      "parameter_scores": [
        {
          "parameter_id": 101,
          "score": 1.5,
          "comments": "Agent greeted customer professionally."
        }
      ]
    }
  ],
  "count": 1
}
```

**With Parameter Details (`include_parameter_details=true`):**
```json
{
  "status": "success",
  "data": [...],
  "count": 1,
  "parameter_definitions": [
    {
      "id": 100,
      "name": "Call Opening",
      "max_score": 2.0,
      "sub_parameters": [
        {
          "id": 101,
          "name": "Professional Greeting",
          "max_score": 1.0
        }
      ]
    }
  ]
}
```

**Error (500 Internal Server Error):**
```json
{
  "status": "error",
  "message": "Failed to retrieve QA results: [error details]"
}
```

#### Examples

**Get all QA results:**
```bash
curl "http://localhost:5012/api/qa-results"
```

**Get QA results with audio URLs:**
```bash
curl "http://localhost:5012/api/qa-results?include_audio=true"
```

**Get complete QA results with all details:**
```bash
curl "http://localhost:5012/api/qa-results?include_parameter_details=true&include_transcript=true&include_audio=true"
```

**Filter by call ID:**
```bash
curl "http://localhost:5012/api/qa-results?call_id=12345"
```

## Audio File Serving

### GET /api/audio/{call_id}/{filename}

Serve audio files for playback in browsers or media players.

#### Request

**Path Parameters:**
- `call_id` (string): The call ID associated with the audio file
- `filename` (string): The name of the audio file

#### Response

**Success (200 OK):**
- **Content-Type**: `audio/wav`, `audio/mpeg`, or `audio/ogg` (based on file extension)
- **Body**: Binary audio data

**Error (404 Not Found):**
```json
{
  "status": "error",
  "message": "Audio file not found"
}
```

**Error (500 Internal Server Error):**
```json
{
  "status": "error",
  "message": "Failed to serve audio file: [error details]"
}
```

#### Example

```bash
curl "http://localhost:5012/api/audio/12345/call_recording.wav" --output downloaded_audio.wav
```

## Error Handling

All API endpoints follow consistent error response formats:

```json
{
  "status": "error",
  "message": "Description of the error"
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request (invalid input)
- `404`: Not Found (resource doesn't exist)
- `409`: Conflict (resource already exists)
- `422`: Unprocessable Entity (validation error)
- `500`: Internal Server Error

## Rate Limiting

Currently, no rate limiting is implemented. For production deployments, consider implementing rate limiting based on your requirements.

## Next Steps

- Review [Voice Call Integration](./Voice_Call_Integration.md) for WebSocket APIs
- Check [Error Handling](./Error_Handling.md) for detailed troubleshooting
- See [Frontend Integration Guide](./Frontend_Integration_Guide.md) for implementation examples
