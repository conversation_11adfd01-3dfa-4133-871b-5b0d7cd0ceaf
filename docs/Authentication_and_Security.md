# Authentication and Security

This document outlines the authentication methods, security considerations, and best practices for the AI Voice Mate system.

## Current Security Implementation

### Authentication Methods

#### 1. Bot Secret Authentication
The system currently uses a simple bot secret mechanism for webhook validation.

**Configuration:**
```env
BOT_SECRET="your_secure_secret_key"
```

**Implementation:**
```python
def two_factor(request: Request) -> bool:
    """Two factor verification for messages coming via webhook."""
    if models.env.bot_secret:
        # Compare secret token from headers
        return secrets.compare_digest(
            request.headers.get('X-Telegram-Bot-Api-Secret-Token', ''), 
            models.env.bot_secret
        )
    return True
```

#### 2. Session-Based Authentication
WebSocket connections use session-based authentication with unique session IDs.

**Session Management:**
```python
# Session creation
session_id = f"session_{random_string}"

# Session validation
user = find_user_by_session(session_id)
if not user:
    logger.warning(f"User with session {session_id} not found")
```

## Security Best Practices

### 1. Environment Variables
Never hardcode sensitive information. Use environment variables for all secrets.

**Secure Configuration:**
```env
# Strong secrets
BOT_SECRET="$(openssl rand -base64 32)"
OPENAI_API_KEY="your_openai_api_key"
DATABASE_URL="postgresql://user:password@localhost/dbname"

# Email credentials
GMAIL_USER="<EMAIL>"
GMAIL_PASS="your_app_specific_password"
```

### 2. Input Validation
Validate all user inputs to prevent injection attacks.

**Example Implementation:**
```python
from pydantic import BaseModel, validator

class UserRegistration(BaseModel):
    name: str
    mobile: str
    userId: str
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Name cannot be empty')
        if len(v) > 100:
            raise ValueError('Name too long')
        return v.strip()
    
    @validator('mobile')
    def validate_mobile(cls, v):
        import re
        if not re.match(r'^\+?[1-9]\d{1,14}$', v):
            raise ValueError('Invalid mobile number format')
        return v
```

### 3. Rate Limiting
Implement rate limiting to prevent abuse.

**FastAPI Rate Limiting:**
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@router.post("/api/tts")
@limiter.limit("10/minute")
async def tts(request: Request):
    # TTS endpoint with rate limiting
    pass
```

### 4. CORS Configuration
Properly configure CORS for production environments.

**Production CORS:**
```python
def enable_cors() -> None:
    origins = [
        "https://yourdomain.com",
        "https://app.yourdomain.com",
        # Remove "*" in production
    ]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["GET", "POST"],
        allow_headers=["authorization", "content-type"],
    )
```

## Production Security Measures

### 1. HTTPS/WSS Implementation

#### Nginx Configuration:
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:5012;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket proxy
    location /ws/ {
        proxy_pass http://localhost:5010;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. Database Security

#### Connection Security:
```python
# Use connection pooling with SSL
DATABASE_URL = "postgresql://user:password@localhost:5432/dbname?sslmode=require"

# Connection with SSL verification
import ssl
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_REQUIRED
```

#### Query Security:
```python
# Use parameterized queries
cursor.execute(
    "SELECT * FROM users WHERE id = ? AND active = ?",
    (user_id, True)
)

# Avoid string concatenation
# BAD: f"SELECT * FROM users WHERE id = {user_id}"
# GOOD: Use parameterized queries as shown above
```

### 3. File Upload Security

#### Secure File Handling:
```python
import os
from pathlib import Path

def secure_filename(filename: str) -> str:
    """Secure a filename by removing dangerous characters."""
    import re
    filename = re.sub(r'[^\w\s-]', '', filename).strip()
    filename = re.sub(r'[-\s]+', '-', filename)
    return filename

@router.post("/api/qa/update-audio-file")
async def qa_update_audio_file(
    call_id: str = Form(...),
    audio_file: UploadFile = File(...),
):
    # Validate file type
    allowed_types = ['audio/wav', 'audio/mpeg', 'audio/ogg']
    if audio_file.content_type not in allowed_types:
        raise HTTPException(400, "Invalid file type")
    
    # Validate file size (e.g., max 50MB)
    max_size = 50 * 1024 * 1024  # 50MB
    contents = await audio_file.read()
    if len(contents) > max_size:
        raise HTTPException(400, "File too large")
    
    # Secure filename
    filename = secure_filename(audio_file.filename)
    
    # Validate call_id format
    if not re.match(r'^[a-zA-Z0-9_-]+$', call_id):
        raise HTTPException(400, "Invalid call_id format")
```

### 4. Logging and Monitoring

#### Security Logging:
```python
import logging
from datetime import datetime

security_logger = logging.getLogger('security')

def log_security_event(event_type: str, user_id: str, details: dict):
    """Log security-related events."""
    security_logger.warning({
        'timestamp': datetime.utcnow().isoformat(),
        'event_type': event_type,
        'user_id': user_id,
        'details': details,
        'ip_address': request.client.host if request else None
    })

# Usage examples
log_security_event('failed_authentication', user_id, {'reason': 'invalid_token'})
log_security_event('rate_limit_exceeded', user_id, {'endpoint': '/api/tts'})
log_security_event('suspicious_file_upload', user_id, {'filename': filename})
```

## Advanced Authentication Implementation

### 1. JWT Token Authentication

#### Token Generation:
```python
import jwt
from datetime import datetime, timedelta

def generate_jwt_token(user_id: str, expires_in_hours: int = 24) -> str:
    """Generate JWT token for user authentication."""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(hours=expires_in_hours),
        'iat': datetime.utcnow(),
        'iss': 'ai-voice-mate'
    }
    return jwt.encode(payload, models.env.jwt_secret, algorithm='HS256')

def verify_jwt_token(token: str) -> dict:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(token, models.env.jwt_secret, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(401, "Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(401, "Invalid token")
```

#### Token Middleware:
```python
from fastapi import Depends, HTTPException
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def get_current_user(token: str = Depends(security)):
    """Get current user from JWT token."""
    payload = verify_jwt_token(token.credentials)
    user_id = payload.get('user_id')
    
    # Fetch user from database
    user = get_user_by_id(user_id)
    if not user:
        raise HTTPException(401, "User not found")
    
    return user

# Protected endpoint
@router.get("/api/protected")
async def protected_endpoint(current_user = Depends(get_current_user)):
    return {"message": f"Hello {current_user.name}"}
```

### 2. API Key Authentication

#### API Key Management:
```python
import secrets
import hashlib

def generate_api_key() -> tuple[str, str]:
    """Generate API key and its hash."""
    api_key = secrets.token_urlsafe(32)
    api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    return api_key, api_key_hash

def verify_api_key(provided_key: str, stored_hash: str) -> bool:
    """Verify API key against stored hash."""
    provided_hash = hashlib.sha256(provided_key.encode()).hexdigest()
    return secrets.compare_digest(provided_hash, stored_hash)

# API Key middleware
async def verify_api_key_header(request: Request):
    """Verify API key from request headers."""
    api_key = request.headers.get('X-API-Key')
    if not api_key:
        raise HTTPException(401, "API key required")
    
    # Check against database
    if not is_valid_api_key(api_key):
        raise HTTPException(401, "Invalid API key")
    
    return api_key
```

### 3. OAuth 2.0 Integration

#### OAuth Configuration:
```python
from authlib.integrations.fastapi_oauth2 import OAuth2AuthorizationCodeBearer

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl="https://accounts.google.com/o/oauth2/auth",
    tokenUrl="https://oauth2.googleapis.com/token",
)

async def get_current_user_oauth(token: str = Depends(oauth2_scheme)):
    """Get current user from OAuth token."""
    # Verify token with OAuth provider
    user_info = await verify_oauth_token(token)
    return user_info
```

## Security Checklist

### Development
- [ ] Use environment variables for all secrets
- [ ] Implement input validation for all endpoints
- [ ] Add rate limiting to prevent abuse
- [ ] Use parameterized database queries
- [ ] Validate file uploads (type, size, content)
- [ ] Implement proper error handling without information leakage
- [ ] Add security logging for audit trails

### Production
- [ ] Enable HTTPS/WSS with valid SSL certificates
- [ ] Configure proper CORS policies
- [ ] Implement authentication and authorization
- [ ] Set up monitoring and alerting
- [ ] Regular security updates and patches
- [ ] Database encryption at rest
- [ ] Network security (firewalls, VPNs)
- [ ] Regular security audits and penetration testing

### Monitoring
- [ ] Log all authentication attempts
- [ ] Monitor for suspicious activities
- [ ] Set up alerts for security events
- [ ] Regular backup and recovery testing
- [ ] Performance monitoring for DDoS detection

## Incident Response

### Security Incident Procedure
1. **Detection**: Monitor logs and alerts
2. **Assessment**: Evaluate the severity and impact
3. **Containment**: Isolate affected systems
4. **Investigation**: Analyze the incident
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Update security measures

### Emergency Contacts
- Security Team: <EMAIL>
- System Administrator: <EMAIL>
- Legal/Compliance: <EMAIL>

## Next Steps

1. Review [Setup and Installation](./Setup_and_Installation.md) for secure deployment
2. Check [Error Handling](./Error_Handling.md) for security-related error responses
3. Implement [Frontend Integration Guide](./Frontend_Integration_Guide.md) with security considerations
4. Regular security audits and updates
