# Frontend Integration Guide

This comprehensive guide provides frontend developers with everything needed to integrate with the AI Voice Mate system, including code examples, best practices, and implementation patterns.

## Overview

The AI Voice Mate system provides two main integration points:
1. **WebSocket API**: Real-time voice communication
2. **REST API**: File operations, QA results, and TTS services

## Quick Start

### 1. Basic Setup

```html
<!DOCTYPE html>
<html>
<head>
    <title>AI Voice Mate Integration</title>
</head>
<body>
    <div id="voice-interface">
        <button id="start-call">Start Voice Call</button>
        <button id="stop-call" disabled>Stop Call</button>
        <div id="status">Ready</div>
        <div id="transcript"></div>
        <audio id="ai-audio" controls></audio>
    </div>
    
    <script src="voice-mate-client.js"></script>
</body>
</html>
```

### 2. JavaScript Client Implementation

```javascript
class VoiceMateClient {
    constructor(config = {}) {
        this.wsUrl = config.wsUrl || 'ws://localhost:5010';
        this.apiUrl = config.apiUrl || 'http://localhost:5012';
        this.sessionId = this.generateSessionId();
        this.ws = null;
        this.mediaRecorder = null;
        this.audioContext = null;
        this.isRecording = false;
        
        // Event callbacks
        this.onConnected = config.onConnected || (() => {});
        this.onDisconnected = config.onDisconnected || (() => {});
        this.onTranscript = config.onTranscript || (() => {});
        this.onAIResponse = config.onAIResponse || (() => {});
        this.onError = config.onError || (() => {});
    }
    
    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9);
    }
    
    async connect() {
        try {
            this.ws = new WebSocket(this.wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.onConnected();
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                this.onDisconnected();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.onError(error);
            };
            
        } catch (error) {
            console.error('Connection failed:', error);
            this.onError(error);
        }
    }
    
    async registerUser(userData) {
        const message = {
            type: 'store_user',
            session: this.sessionId,
            data: {
                name: userData.name,
                mobile: userData.mobile || '',
                userId: userData.userId,
                sentences: [],
                sessionType: 'call',
                target: 'english_tutor'
            }
        };
        
        this.sendMessage(message);
    }
    
    async startVoiceCall() {
        // Start AI call
        this.sendMessage({
            type: 'start_ai_call',
            session: this.sessionId,
            data: null
        });
        
        // Enable AI listening
        this.sendMessage({
            type: 'ai_start_listening',
            session: this.sessionId,
            data: null
        });
        
        // Start recording user audio
        await this.startRecording();
    }
    
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                } 
            });
            
            this.audioContext = new AudioContext({ sampleRate: 16000 });
            const source = this.audioContext.createMediaStreamSource(stream);
            
            // Create audio processor
            const processor = this.audioContext.createScriptProcessor(1024, 1, 1);
            processor.onaudioprocess = (event) => {
                if (this.isRecording) {
                    const audioData = event.inputBuffer.getChannelData(0);
                    this.sendAudioChunk(audioData);
                }
            };
            
            source.connect(processor);
            processor.connect(this.audioContext.destination);
            
            this.isRecording = true;
            console.log('Recording started');
            
        } catch (error) {
            console.error('Failed to start recording:', error);
            this.onError(error);
        }
    }
    
    sendAudioChunk(audioData) {
        // Convert Float32Array to Int16Array
        const int16Array = new Int16Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
            int16Array[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));
        }
        
        // Convert to regular array for JSON serialization
        const audioArray = Array.from(int16Array);
        
        this.sendMessage({
            type: 'audio_chunk',
            session: this.sessionId,
            data: audioArray
        });
    }
    
    stopRecording() {
        this.isRecording = false;
        
        this.sendMessage({
            type: 'voice_action_stop',
            session: this.sessionId,
            data: 'stop_recording'
        });
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
    }
    
    endCall() {
        this.stopRecording();
        
        this.sendMessage({
            type: 'end_ai_call',
            session: this.sessionId,
            data: null
        });
    }
    
    sendMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.error('WebSocket not connected');
        }
    }
    
    handleMessage(message) {
        console.log('Received message:', message);
        
        switch (message.type) {
            case 'store_user':
                console.log('User registered successfully');
                break;
                
            case 'transcript_batch':
                this.onTranscript(message.data);
                break;
                
            case 'speech_text':
                this.onTranscript(message.data, true); // Final transcript
                break;
                
            case 'llm_answer':
                this.playAudioResponse(message.data);
                break;
                
            case 'ai_response':
                this.onAIResponse(message.data);
                break;
                
            case 'pause_audio_recording':
                this.isRecording = false;
                break;
                
            case 'ai_end_call':
                console.log('AI call ended');
                this.endCall();
                break;
                
            case 'error':
                console.error('Server error:', message.message);
                this.onError(new Error(message.message));
                break;
                
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    async playAudioResponse(base64Audio) {
        try {
            // Decode base64 audio
            const audioData = atob(base64Audio);
            const audioArray = new Uint8Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                audioArray[i] = audioData.charCodeAt(i);
            }
            
            // Create audio blob and play
            const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            const audio = new Audio(audioUrl);
            audio.play();
            
            // Clean up URL after playing
            audio.onended = () => {
                URL.revokeObjectURL(audioUrl);
            };
            
        } catch (error) {
            console.error('Failed to play audio response:', error);
            this.onError(error);
        }
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
        this.stopRecording();
    }
}
```

## React Integration

### 1. React Hook Implementation

```jsx
import { useState, useEffect, useRef, useCallback } from 'react';

export const useVoiceMate = (config = {}) => {
    const [isConnected, setIsConnected] = useState(false);
    const [isRecording, setIsRecording] = useState(false);
    const [transcript, setTranscript] = useState('');
    const [aiResponse, setAiResponse] = useState('');
    const [error, setError] = useState(null);
    
    const clientRef = useRef(null);
    
    useEffect(() => {
        const client = new VoiceMateClient({
            ...config,
            onConnected: () => setIsConnected(true),
            onDisconnected: () => setIsConnected(false),
            onTranscript: (text, isFinal) => {
                if (isFinal) {
                    setTranscript(text);
                }
            },
            onAIResponse: (text) => setAiResponse(text),
            onError: (err) => setError(err.message)
        });
        
        clientRef.current = client;
        
        return () => {
            client.disconnect();
        };
    }, []);
    
    const connect = useCallback(async () => {
        if (clientRef.current) {
            await clientRef.current.connect();
        }
    }, []);
    
    const registerUser = useCallback(async (userData) => {
        if (clientRef.current) {
            await clientRef.current.registerUser(userData);
        }
    }, []);
    
    const startCall = useCallback(async () => {
        if (clientRef.current) {
            setIsRecording(true);
            await clientRef.current.startVoiceCall();
        }
    }, []);
    
    const endCall = useCallback(() => {
        if (clientRef.current) {
            setIsRecording(false);
            clientRef.current.endCall();
        }
    }, []);
    
    return {
        isConnected,
        isRecording,
        transcript,
        aiResponse,
        error,
        connect,
        registerUser,
        startCall,
        endCall
    };
};
```

### 2. React Component Example

```jsx
import React, { useState } from 'react';
import { useVoiceMate } from './hooks/useVoiceMate';

const VoiceInterface = () => {
    const [user, setUser] = useState({
        name: '',
        userId: '',
        mobile: ''
    });
    
    const {
        isConnected,
        isRecording,
        transcript,
        aiResponse,
        error,
        connect,
        registerUser,
        startCall,
        endCall
    } = useVoiceMate({
        wsUrl: process.env.REACT_APP_WS_URL,
        apiUrl: process.env.REACT_APP_API_URL
    });
    
    const handleConnect = async () => {
        await connect();
        if (user.name && user.userId) {
            await registerUser(user);
        }
    };
    
    return (
        <div className="voice-interface">
            <div className="user-form">
                <input
                    type="text"
                    placeholder="Your Name"
                    value={user.name}
                    onChange={(e) => setUser({...user, name: e.target.value})}
                />
                <input
                    type="text"
                    placeholder="User ID"
                    value={user.userId}
                    onChange={(e) => setUser({...user, userId: e.target.value})}
                />
                <input
                    type="tel"
                    placeholder="Mobile Number"
                    value={user.mobile}
                    onChange={(e) => setUser({...user, mobile: e.target.value})}
                />
            </div>
            
            <div className="controls">
                <button 
                    onClick={handleConnect}
                    disabled={isConnected || !user.name || !user.userId}
                >
                    {isConnected ? 'Connected' : 'Connect'}
                </button>
                
                <button 
                    onClick={startCall}
                    disabled={!isConnected || isRecording}
                >
                    Start Call
                </button>
                
                <button 
                    onClick={endCall}
                    disabled={!isRecording}
                >
                    End Call
                </button>
            </div>
            
            <div className="status">
                <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
                    {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
                </div>
                
                <div className={`recording-status ${isRecording ? 'recording' : ''}`}>
                    {isRecording ? '🎤 Recording...' : '🎤 Not Recording'}
                </div>
            </div>
            
            <div className="conversation">
                <div className="transcript">
                    <h3>Your Speech:</h3>
                    <p>{transcript || 'Start speaking...'}</p>
                </div>
                
                <div className="ai-response">
                    <h3>AI Response:</h3>
                    <p>{aiResponse || 'AI will respond here...'}</p>
                </div>
            </div>
            
            {error && (
                <div className="error">
                    <strong>Error:</strong> {error}
                </div>
            )}
        </div>
    );
};

export default VoiceInterface;
```

## REST API Integration

### 1. API Client Class

```javascript
class VoiceMateAPI {
    constructor(baseUrl = 'http://localhost:5012') {
        this.baseUrl = baseUrl;
    }
    
    async textToSpeech(message) {
        try {
            const response = await fetch(`${this.baseUrl}/api/tts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const audioBlob = await response.blob();
            return URL.createObjectURL(audioBlob);
            
        } catch (error) {
            console.error('TTS request failed:', error);
            throw error;
        }
    }
    
    async uploadAudioFile(callId, audioFile, overwrite = false) {
        try {
            const formData = new FormData();
            formData.append('call_id', callId);
            formData.append('audio_file', audioFile);
            formData.append('overwrite', overwrite.toString());
            
            const response = await fetch(`${this.baseUrl}/api/qa/update-audio-file`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || 'Upload failed');
            }
            
            return result;
            
        } catch (error) {
            console.error('Audio upload failed:', error);
            throw error;
        }
    }
    
    async getQAResults(params = {}) {
        try {
            const queryParams = new URLSearchParams();
            
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    queryParams.append(key, value.toString());
                }
            });
            
            const url = `${this.baseUrl}/api/qa-results?${queryParams}`;
            const response = await fetch(url);
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || 'Failed to fetch QA results');
            }
            
            return result;
            
        } catch (error) {
            console.error('QA results request failed:', error);
            throw error;
        }
    }
    
    async getAudioFile(callId, filename) {
        try {
            const response = await fetch(`${this.baseUrl}/api/audio/${callId}/${filename}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const audioBlob = await response.blob();
            return URL.createObjectURL(audioBlob);
            
        } catch (error) {
            console.error('Audio file request failed:', error);
            throw error;
        }
    }
}
```

### 2. React API Hook

```jsx
import { useState, useCallback } from 'react';

export const useVoiceMateAPI = (baseUrl) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    
    const api = new VoiceMateAPI(baseUrl);
    
    const executeRequest = useCallback(async (requestFn) => {
        setLoading(true);
        setError(null);
        
        try {
            const result = await requestFn();
            return result;
        } catch (err) {
            setError(err.message);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);
    
    const textToSpeech = useCallback((message) => {
        return executeRequest(() => api.textToSpeech(message));
    }, [executeRequest]);
    
    const uploadAudio = useCallback((callId, file, overwrite) => {
        return executeRequest(() => api.uploadAudioFile(callId, file, overwrite));
    }, [executeRequest]);
    
    const getQAResults = useCallback((params) => {
        return executeRequest(() => api.getQAResults(params));
    }, [executeRequest]);
    
    const getAudioFile = useCallback((callId, filename) => {
        return executeRequest(() => api.getAudioFile(callId, filename));
    }, [executeRequest]);
    
    return {
        loading,
        error,
        textToSpeech,
        uploadAudio,
        getQAResults,
        getAudioFile
    };
};
```

## Error Handling

### 1. Client-Side Error Handling

```javascript
class ErrorHandler {
    static handle(error, context = '') {
        console.error(`Error in ${context}:`, error);
        
        // Categorize errors
        if (error.name === 'NetworkError' || error.message.includes('fetch')) {
            return {
                type: 'network',
                message: 'Network connection failed. Please check your internet connection.',
                retry: true
            };
        }
        
        if (error.message.includes('WebSocket')) {
            return {
                type: 'websocket',
                message: 'Real-time connection lost. Attempting to reconnect...',
                retry: true
            };
        }
        
        if (error.message.includes('microphone') || error.message.includes('getUserMedia')) {
            return {
                type: 'microphone',
                message: 'Microphone access denied. Please allow microphone permissions.',
                retry: false
            };
        }
        
        return {
            type: 'unknown',
            message: error.message || 'An unexpected error occurred.',
            retry: false
        };
    }
    
    static async retry(operation, maxRetries = 3, delay = 1000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await operation();
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
            }
        }
    }
}
```

## Best Practices

### 1. Performance Optimization

```javascript
// Debounce audio processing
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Optimize audio chunk sending
const optimizedSendAudioChunk = debounce((audioData) => {
    // Process and send audio chunk
    sendAudioChunk(audioData);
}, 100); // Send chunks every 100ms
```

### 2. Memory Management

```javascript
class AudioManager {
    constructor() {
        this.audioUrls = new Set();
    }
    
    createAudioUrl(blob) {
        const url = URL.createObjectURL(blob);
        this.audioUrls.add(url);
        return url;
    }
    
    cleanup() {
        this.audioUrls.forEach(url => URL.revokeObjectURL(url));
        this.audioUrls.clear();
    }
}
```

### 3. Security Considerations

```javascript
// Validate WebSocket messages
function validateMessage(message) {
    const requiredFields = ['type', 'session'];
    const allowedTypes = ['store_user', 'start_ai_call', 'audio_chunk', 'end_ai_call'];
    
    if (!requiredFields.every(field => field in message)) {
        throw new Error('Invalid message format');
    }
    
    if (!allowedTypes.includes(message.type)) {
        throw new Error('Invalid message type');
    }
    
    return true;
}

// Sanitize user inputs
function sanitizeInput(input) {
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
}
```

## Testing

### 1. Unit Tests

```javascript
// Jest test example
describe('VoiceMateClient', () => {
    let client;
    
    beforeEach(() => {
        client = new VoiceMateClient({
            wsUrl: 'ws://localhost:5010',
            apiUrl: 'http://localhost:5012'
        });
    });
    
    test('should generate unique session ID', () => {
        const sessionId1 = client.generateSessionId();
        const sessionId2 = client.generateSessionId();
        
        expect(sessionId1).not.toBe(sessionId2);
        expect(sessionId1).toMatch(/^session_[a-z0-9]+$/);
    });
    
    test('should handle WebSocket messages correctly', () => {
        const mockCallback = jest.fn();
        client.onTranscript = mockCallback;
        
        const message = {
            type: 'transcript_batch',
            data: 'Hello world'
        };
        
        client.handleMessage(message);
        expect(mockCallback).toHaveBeenCalledWith('Hello world');
    });
});
```

### 2. Integration Tests

```javascript
// Cypress test example
describe('Voice Interface Integration', () => {
    beforeEach(() => {
        cy.visit('/voice-interface');
    });
    
    it('should connect and start voice call', () => {
        cy.get('[data-testid="name-input"]').type('Test User');
        cy.get('[data-testid="user-id-input"]').type('test_001');
        cy.get('[data-testid="connect-button"]').click();
        
        cy.get('[data-testid="connection-status"]').should('contain', 'Connected');
        
        cy.get('[data-testid="start-call-button"]').click();
        cy.get('[data-testid="recording-status"]').should('contain', 'Recording');
    });
});
```

## Next Steps

1. Review [Voice Call Integration](./Voice_Call_Integration.md) for detailed WebSocket protocol
2. Check [API Reference](./API_Reference.md) for complete endpoint documentation
3. Follow [Error Handling](./Error_Handling.md) for comprehensive error management
4. Implement [Authentication and Security](./Authentication_and_Security.md) measures
