import os
import json
import time
from typing import Dict, List, Tuple
import assemblyai as aai
from dotenv import load_dotenv

class HinglishTranscriber:
    """Transcription service for Hinglish (Hindi-English mixed) audio using AssemblyAI."""
    
    def __init__(self, logger):
        load_dotenv()
        self.logger = logger
        self.api_key = os.getenv('ASSEMBLYAI_API_KEY')
        aai.settings.api_key = self.api_key
        
    async def transcribe_audio(self, audio_file_path: str) -> Dict:
        """
        Transcribe audio file with speaker diarization and Hinglish optimization.
        
        Args:
            audio_file_path: Path to the audio file
            
        Returns:
            Dict containing transcription results with speaker labels
        """
        try:
            # Configure transcription with speaker diarization
            config = aai.TranscriptionConfig(
                speaker_labels=True,
                # Use language detection instead of specifying language code for Hinglish
                language_detection=True,  # Enable language detection for mixed language
                punctuate=True,
                format_text=True,
                speakers_expected=2
            )
            
            # Start transcription
            transcriber = aai.Transcriber()
            transcript = transcriber.transcribe(audio_file_path, config)
            
            # Process results
            if transcript.status == "completed":
                return self._format_transcript(transcript)
            else:
                self.logger.error(f"Transcription failed with status: {transcript.status}")
                return {"error": f"Transcription failed: {transcript.status}"}
                
        except Exception as e:
            self.logger.error(f"Error in transcription: {str(e)}")
            return {"error": str(e)}
    
    def _format_transcript(self, transcript) -> Dict:
        """Format the transcript with speaker information."""
        result = {
            "transcript_text": transcript.text,
            "utterances": []
        }
        
        # Process utterances with speaker information
        for utterance in transcript.utterances:
            result["utterances"].append({
                "speaker": "Assistant" if utterance.speaker == "A" else "Customer",
                "text": utterance.text,
                "start": utterance.start,
                "end": utterance.end
            })
            
        return result
    
    async def save_to_database(self, transcript_data: Dict, call_id: str, db) -> bool:
        """Save transcription results to database."""
        try:
            # Create transcript record
            transcript_record = {
                "call_id": call_id,
                "timestamp": time.time(),
                "full_transcript": transcript_data["transcript_text"],
                "utterances": json.dumps(transcript_data["utterances"])
            }
            
            # Save to database
            db.insert_record("transcripts", transcript_record)
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving transcript to database: {str(e)}")
            return False
