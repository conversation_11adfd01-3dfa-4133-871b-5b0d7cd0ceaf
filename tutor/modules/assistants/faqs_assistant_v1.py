import json
import os
import time
from typing import Op<PERSON>, Tu<PERSON>, List, Dict, Any
import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai.types.chat.chat_completion import ChatCompletion
from tutor.api.faqs_service import FaqsService, FAQsData
from tutor.executors import files
from threading import Thread

# Load environment variables
load_dotenv()
# Ensure OPENAI_API_KEY is always a string if it exists
openai_api_key = os.getenv('OPENAI_API_KEY')
if openai_api_key:
    os.environ["OPENAI_API_KEY"] = openai_api_key

class FaqsAssistant:
    """AI-powered assistant class to handle FAQ queries."""

    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: Optional[Any], target: Optional[str]):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-4o-mini"  # "gpt-3.5-turbo" "gpt-4o" "gpt-4o-mini"
        self.mobile = mobile
        self.client = openai
        self.faqs_service = FaqsService(logger)  # Initialize FAQ service
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, how can I help you with your questions today?"
        self.ongo_context = ""

    async def handle_response(self, assistant_response: ChatCompletion):
        """Processes the assistant's response and takes necessary actions."""
        function_call = assistant_response.choices[0].message.function_call
        if function_call and function_call.name == "get_faq_answer":
            self.conversation_history.append(assistant_response.choices[0].message)

            arguments = json.loads(function_call.arguments)
            query = arguments.get("query")

            if query:
                faq_data: Optional[FAQsData] = await self.faqs_service.get_best_match(query) # Changed type hint to Optional
                if faq_data:
                    response_content = faq_data.answer
                else:
                    response_content = "I couldn't find an answer to that question in the FAQs. Can you please rephrase or ask something else?"

                self.conversation_history.append({"role": "function", "name": function_call.name, "content": response_content})
                chat_response = self._get_chat_response(self.conversation_history)
                return await self.handle_response(chat_response)
            else:
                return "I need a query to search the FAQs. Please provide your question."

        return str(assistant_response.choices[0].message.content)

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for FAQ services."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    async def query(self, sentence: str, context: str) -> str:
        """Handles user queries for FAQs."""
        self.conversation_history.append(self._create_message("user", sentence))

        is_faqs, context = await self._handle_faqs_query(sentence)
        self.ongo_context = context # Set the context here

        messages_for_llm = self.conversation_history[-12:]
        system_message = self._create_message("system", self._generate_initial_content())
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message

        response_time = 0.0 # Initialize response_time
        response, response_time = await self._get_chat_response_with_timing(messages_for_llm)
        if response:
            # Ensure response is a string before appending
            if isinstance(response, str):
                self.conversation_history.append(self._create_message("assistant", response))
            else:
                self.logger.warning(f"Unexpected response type from LLM: {type(response)}. Content: {response}")
                self.conversation_history.append(self._create_message("assistant", str(response)))
            Thread(target=self._dump_history, args=(sentence, response, f"{response_time:.2f}")).start()
        return response

    async def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        """Gets a response from the GPT model and tracks the time taken."""
        try:
            start_time = time.time()
            chat_response = self._get_chat_response(messages)
            end_time = time.time()
            response_time = end_time - start_time
        except OpenAIError as error:
            self.logger.error(error)
            return "Sorry, I can't process your request right now.", 0

        if chat_response.choices:
            return await self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def _generate_initial_content(self) -> str:
        """Generates the system prompt for the FAQ assistant."""
        base_instructions = (
            f'You are an FAQ assistant, providing answers to frequently asked questions. '
            "You are a professional and courteous customer care representative assisting users via voice call. "
            "The user is a non-native English speaker, and the transcribed input may contain errors due to mispronunciations. "
            "Please interpret the user's input carefully, considering possible mispronunciations, and seek clarification if needed. "
            "Respond only in English. Your responses will be audio-fed, so keep them concise within 1 to 4 sentences without any parentheses.\n"
        )

        specific_instructions = (
            "Your responsibilities:\n"
            "- Use all relevant information in the provided context to answer the user's question as accurately as possible.\n"
            "- Do not rely on external knowledge or make up information not found in the context.\n"
            "- If the context does not contain an exact answer, try to:\n"
            "  - Infer a helpful response based on what is available.\n"
            "  - Clearly explain that the context lacks enough detail to provide a complete answer, if necessary.\n"
            "\n"
            "Guidelines for your answers:\n"
            "- Be clear, concise, and helpful.\n"
            "- Maintain a professional and respectful tone.\n"
            "- When helpful, quote or refer to the relevant part of the context.\n"
            "- Do not mention that you are an AI or describe your behavior.\n"
            "- Avoid disclaimers like 'based on the context provided...' — assume the user knows you're working within context.\n"
            "\n"
            "Example behaviors:\n"
            "- ✅ If the context includes relevant data, answer the question directly using that data.\n"
            "- ❌ If the answer is not found, do not make it up. Instead, acknowledge the gap and, if possible, suggest what might help.\n"
            "\n"
            "Always prioritize helpfulness without guessing."
        )

        # Format the context properly
        ongo_context_formatted = f"Context:\n{self.ongo_context}\n\n" if self.ongo_context else ""

        # Combine all parts to form the final prompt
        prompt = (
            f"{base_instructions}"
            f"{ongo_context_formatted}"
            f"{specific_instructions}"
        )
        return prompt

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file.

        Args:
            request: The request from the user.
            response: The response from the assistant.
            response_time: The time taken for the response.
        """
        clear_model = self.model.replace('.', '_').replace('-', '_')
        file_name = f"{clear_model}.yaml"
        data = files.load_yaml_from_session(self.mobile, self.session, file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, file_name, data)

    async def _handle_faqs_query(self, phrase: str) -> Tuple[bool, str]:
        """
        Handles queries related to general FAQs.

        :param phrase: The query phrase for general FAQs.
        :return: A tuple containing a boolean indicating if a match was found
                 and the answer from the FAQsData instance.
        """
        faq_response = await self.faqs_service.get_best_match(phrase)
        if faq_response:
            return True, faq_response.answer
        else:
            return False, "No answer available."
