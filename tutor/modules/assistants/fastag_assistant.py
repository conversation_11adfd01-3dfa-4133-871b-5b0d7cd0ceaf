from datetime import datetime
import json
import os
import re
import time
from typing import Op<PERSON>, Tu<PERSON>, List, Dict

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai.types.chat import ChatCompletion
import uuid

# Assuming you have similar modules for handling files and database operations
from tutor.executors import files
from tutor.modules.database import database
from tutor.modules.database.msql_database import Database
from tutor.modules.models import models

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
db = database.Database(database=models.fileio.base_db)

class FastagAccountManager:
    """Class to handle client FASTag details and transactions based on IDFC FASTag management data."""

    def __init__(self, mobile: str):
        self.mobile = mobile
        self.company_name = ""
        self.account_id = ""
        self.client_name = ""
        self.fastag_balance = 0.0
        self.vehicle_details = {}  # Stores vehicle information linked with FASTag
        self.transactions = []  # List of FASTag transaction details
        self._load_client_data()

    def _load_client_data(self):
        """Loads the client-specific FASTag data, account details, and transaction histories based on the mobile number."""

        # For demonstration, we're using hardcoded data. In practice, load from a database or external source.
        client_data = {
            "+************": {
                "account_id": "F001",
                "client_name": "Mayur Badkat",
                "fastag_balance": 1500.0,
                "vehicle_details": {
                    "MH12AB1234": "Car",
                    "MH14CD5678": "SUV"
                },
                "transactions": [
                    {"transaction_id": "FT001", "vehicle_number": "MH12AB1234", "amount": 200.0, "date": "2024-09-10", "type": "toll deduction"},
                    {"transaction_id": "FT002", "vehicle_number": "MH14CD5678", "amount": 150.0, "date": "2024-09-11", "type": "toll deduction"},
                    {"transaction_id": "FT003", "vehicle_number": "MH12AB1234", "amount": 300.0, "date": "2024-09-15", "type": "recharge"},
                    {"transaction_id": "FT004", "vehicle_number": "MH14CD5678", "amount": 250.0, "date": "2024-09-20", "type": "recharge"},
                    {"transaction_id": "FT005", "vehicle_number": "MH12AB1234", "amount": 100.0, "date": "2024-09-22", "type": "toll deduction"}
                ]
            },
            "+************": {
                "account_id": "F002",
                "client_name": "Nikesh Samaiya",
                "fastag_balance": 2500.0,
                "vehicle_details": {
                    "GJ01XY9876": "Truck",
                    "GJ05PQ5432": "Van"
                },
                "transactions": [
                    {"transaction_id": "FT006", "vehicle_number": "GJ01XY9876", "amount": 500.0, "date": "2024-09-11", "type": "toll deduction"},
                    {"transaction_id": "FT007", "vehicle_number": "GJ05PQ5432", "amount": 700.0, "date": "2024-09-13", "type": "recharge"},
                    {"transaction_id": "FT008", "vehicle_number": "GJ01XY9876", "amount": 300.0, "date": "2024-09-15", "type": "toll deduction"},
                    {"transaction_id": "FT009", "vehicle_number": "GJ05PQ5432", "amount": 400.0, "date": "2024-09-18", "type": "recharge"},
                    {"transaction_id": "FT010", "vehicle_number": "GJ01XY9876", "amount": 600.0, "date": "2024-09-20", "type": "toll deduction"}
                ]
            }
        }

        # Set client data to instance variables
        user_data = client_data.get(self.mobile)
        if user_data:
            self.account_id = user_data["account_id"]
            self.client_name = user_data["client_name"]
            self.fastag_balance = user_data["fastag_balance"]
            self.vehicle_details = user_data.get("vehicle_details", {})
            self.transactions = user_data["transactions"]

    def get_recent_transactions(self) -> List[Dict]:
        """Returns the client's last 5 FASTag transactions."""
        return self.transactions[-5:]

    def get_recent_transactions_summary(self) -> str:
        """Returns a summary of the client's last 5 FASTag transactions as a formatted string."""
        return "\n".join(
            [f"Transaction ID: {tx['transaction_id']}, Type: {tx['type']}, Vehicle: {tx['vehicle_number']}, Amount: Rupees {tx['amount']}, Date: {tx['date']}" for tx in self.get_recent_transactions()]
        )

    def get_fastag_balance(self) -> dict:
        """Returns the current FASTag balance for the client's account."""
        return {
            "fastag_balance": self.fastag_balance
        }

    def get_vehicle_details(self) -> Dict[str, str]:
        """Returns the current vehicle details associated with FASTag."""
        return self.vehicle_details

    def get_transaction_history(self) -> List[Dict]:
        """Returns the transaction history for the account."""
        return self.transactions

    def validate_transaction(self, transaction_id: str) -> bool:
        """Checks if the transaction ID exists in the client's transaction history."""
        return any(tx["transaction_id"] == transaction_id for tx in self.get_transaction_history())

    def create_support_ticket(self, case_type: str, subject: str, description: str) -> dict:
        """Creates a support ticket in the database and returns a confirmation."""
        case_id = db.insert_support_ticket(self.account_id, self.client_name, case_type, subject, description)
        return {
            "case_id": case_id,
            "status": "success",
        }

    def recharge_fastag(self, amount: float) -> dict:
        """Executes a FASTag recharge operation."""
        if amount <= 0:
            return {"status": "error", "message": "Please provide a valid recharge amount."}

        transaction_id = f"FT{len(self.transactions)+1:03d}"
        transaction = {
            "transaction_id": transaction_id,
            "vehicle_number": "Recharge",  # For recharge transactions
            "amount": amount,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "type": "recharge"
        }
        self.transactions.append(transaction)
        self.fastag_balance += amount
        return {
            "status": "success",
            "transaction": transaction,
            "new_balance": self.fastag_balance
        }

    def deduct_toll(self, vehicle_number: str, amount: float) -> dict:
        """Simulates a toll deduction from the FASTag account."""
        if vehicle_number not in self.vehicle_details:
            return {"status": "error", "message": f"{vehicle_number} is not registered with your FASTag account."}

        if amount > self.fastag_balance:
            return {"status": "error", "message": "Insufficient balance in FASTag account."}

        transaction_id = f"FT{len(self.transactions)+1:03d}"
        transaction = {
            "transaction_id": transaction_id,
            "vehicle_number": vehicle_number,
            "amount": amount,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "type": "toll deduction"
        }
        self.transactions.append(transaction)
        self.fastag_balance -= amount
        return {
            "status": "success",
            "transaction": transaction,
            "new_balance": self.fastag_balance
        }


class FastagAssistant:
    """AI-powered assistant class to handle FASTag services, including balance inquiry, recharge, and toll deductions."""

    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None, target: str):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-4o-mini"
        self.mobile = mobile
        self.client = openai
        self.fastag_manager = FastagAccountManager(mobile)
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to {self.fastag_manager.company_name} fast tag Services! How can I assist you today?"
        self.functions = self._create_functions()

    def _create_functions(self) -> List[Dict]:
        """Define the functions to handle FASTag balance inquiry, transaction history, recharge, toll deduction, and support ticket creation."""
        return [
            {
                "name": "manage_fastag",
                "description": "Handles fast tag balance inquiries, transaction history requests, recharge operations, toll deductions, and support ticket creation by inferring details from the client's input.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "operation": {
                            "type": "string",
                            "enum": ["balance_inquiry", "transaction_history", "recharge", "toll_deduction", "create_support_ticket"],
                            "description": "The type of operation inferred from the client's request."
                        },
                        "vehicle_number": {
                            "type": "string",
                            "description": "The vehicle number involved in the transaction. Required for toll deduction."
                        },
                        "amount": {
                            "type": "number",
                            "description": "The amount for the transaction in Rupees. Required for recharge and toll deduction operations."
                        },
                        "ticket_type": {
                            "type": "string",
                            "enum": ["query", "request", "complaint", "feedback"],
                            "description": "The inferred type of support ticket. Required for 'create_support_ticket' operation."
                        },
                        "subject": {
                            "type": "string",
                            "description": "The inferred subject of the support ticket. Required for 'create_support_ticket' operation."
                        },
                        "description": {
                            "type": "string",
                            "description": "The detailed description, as inferred from the client's input. Required for 'create_support_ticket' operation."
                        },
                        "confirmed": {
                            "type": "boolean",
                            "description": "Indicates whether the client has confirmed the description. Required for 'create_support_ticket' operation."
                        }
                    },
                    "required": ["operation"]
                }
            }
        ]

    def handle_response(self, assistant_response: ChatCompletion):
        """Processes the assistant's response and takes necessary actions."""
        function_call = assistant_response.choices[0].message.function_call
        if function_call and function_call.name == "manage_fastag":
            message = assistant_response.choices[0].message
            message_dict = {
                "role": message.role,
                "name": function_call.name,
                "content": message.content,
                "function_call": message.function_call
            }
            self.conversation_history.append(message_dict)

            arguments = json.loads(function_call.arguments)
            operation = arguments.get("operation")
            vehicle_number = arguments.get("vehicle_number", "")
            amount = arguments.get("amount", 0)
            ticket_type = arguments.get("ticket_type", "")
            subject = arguments.get("subject", "")
            description = arguments.get("description", "")
            confirmed = arguments.get("confirmed", False)

            if operation == "balance_inquiry":
                balance_info = self.fastag_manager.get_fastag_balance()
                vehicles = self.fastag_manager.get_vehicle_details()
                response_data = {
                    "fastag_balance": balance_info["fastag_balance"],
                    "vehicles": vehicles
                }
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(response_data)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "transaction_history":
                transactions = self.fastag_manager.get_transaction_history()
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(transactions)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "recharge":
                if amount <= 0:
                    return "Please provide a valid recharge amount."
                recharge_response = self.fastag_manager.recharge_fastag(amount)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(recharge_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "toll_deduction":
                if not vehicle_number or amount <= 0:
                    return "Please provide the vehicle number and a valid amount for toll deduction."
                deduction_response = self.fastag_manager.deduct_toll(vehicle_number, amount)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(deduction_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "create_support_ticket":
                if not ticket_type or not subject or not description:
                    return "Please provide the ticket type, subject, and description for creating a support ticket."
                ticket_response = self.fastag_manager.create_support_ticket(ticket_type, subject, description)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(ticket_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

        return assistant_response.choices[0].message.content

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Client"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for FASTag services."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    def query(self, sentence: str, context: str) -> str:
        """Handles client queries such as balance inquiries, recharges, toll deductions, and support tickets."""
        self.conversation_history.append(self._create_message("user", sentence))

        messages_for_llm = self.conversation_history[-12:]
        system_message = self._create_message("system", self._generate_initial_content())
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message

        response, _ = self._get_chat_response_with_timing(messages_for_llm)
        if response:
            self.conversation_history.append(self._create_message("assistant", response))
        return response.replace("fastag","fast tag").replace("FASTag","fast tag")

    def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        """Gets a response from the GPT model and tracks the time taken."""
        try:
            start_time = time.time()
            chat_response = self._get_chat_response(messages)
            end_time = time.time()
            response_time = end_time - start_time
        except OpenAIError as error:
            self.logger.error(error)
            return "Sorry, I can't process your request right now.", 0

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with available FASTag services and client details."""
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        base_instructions = (
            f'You are a fast tag assistant named "Lisa", providing fast tag services for {self.fastag_manager.company_name}. '
            "You are a professional and courteous customer service assistant helping clients over a voice call. "
            "The client is a non-native English speaker, and the transcribed input may contain errors due to mispronunciations. "
            "Please interpret the client's input carefully, considering possible mispronunciations, and seek clarification if needed. "
            "Respond only in English. Your responses will be audio-fed, so keep them concise within 1 to 2 sentences without any parentheses.\n"
            f"Today's date and time is {current_date_time}.\n\n"
        )

        client_info = f"Client Name: {self.fastag_manager.client_name}, fast tag Balance: Rupees {self.fastag_manager.fastag_balance}"
        vehicles = "\n".join([f"{vehicle}: {type}" for vehicle, type in self.fastag_manager.get_vehicle_details().items()])
        vehicle_info = f"Here are the registered vehicles:\n{vehicles}\n\n" if vehicles else "No vehicles registered.\n\n"

        recent_transactions = self.fastag_manager.get_recent_transactions_summary()
        transactions_info = f"Here are the Recent Transactions:\n{recent_transactions}\n\n" if recent_transactions else "No recent transactions available.\n\n"

        common_instructions = (
            "- Keep the conversation short and simple.\n"
            "- Provide feedback if any information is missing or needs clarification. Respond clearly if the client's input is out of scope.\n"
        )

        specific_instructions = (
            "\n\nInstructions:\n"
            "- Assist clients with balance inquiries, transaction history requests, recharge operations, toll deductions, and support ticket creation.\n"
            "- Infer the client's intended operation (balance inquiry, transaction history, recharge, toll deduction, create support ticket) from their input without asking them directly.\n"
            "- If recharging fast tag or deducting toll:\n"
            "   - Confirm the amount with the client before proceeding.\n"
            "   - Provide a summary of the transaction once completed.\n"
            "- If creating a support ticket:\n"
            "   - Analyze the client's message to determine the ticket type (query, request, complaint, feedback).\n"
            "   - Create an appropriate subject that summarizes the client's input.\n"
            "   - Generate a detailed description based on the client's input.\n"
        )

        prompt = (
            f"{base_instructions}"
            f"{client_info}\n\n"
            f"{vehicle_info}"
            f"{transactions_info}"
            f"{specific_instructions}"
            f"{common_instructions}"
        )

        return prompt

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)
