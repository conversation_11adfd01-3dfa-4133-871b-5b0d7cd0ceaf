from datetime import datetime
import json
import os
import re
import time
from typing import Op<PERSON>, Tu<PERSON>, List, Dict

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai.types.chat.chat_completion import ChatCompletion
from tutor.executors import files
import uuid

from tutor.modules.database import database
from tutor.modules.database.msql_database import Database
from tutor.modules.models import models 

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
# db = Database(database="sampledb", server="192.168.1.5,1433", user="mayur", password="123456")
db = database.Database(database=models.fileio.base_db)

class BankAccountManager:
    """Class to handle account details and transactions based on bank data."""
    
    def __init__(self, mobile: str):
        self.mobile = mobile
        self.bank_name = ""
        self.account_id = ""
        self.customer_name = ""
        self.balance = 0.0
        self.accounts = {}  # Stores account information and balances
        self.transactions = []  # List of transaction details
        self._load_user_data()

    def _load_user_data(self):
        """Loads the user-specific bank data, account details, and transaction histories based on the mobile number."""
        # For demonstration, we're using hardcoded data. In practice, load from a database or external source.
        bank_data = {
            "+************": {
                "account_id": "A001",
                "bank_name": "ICICI Bank",
                "customer_name": "Mayur Badkat",
                "balance": 5000,                
                "transactions": [
                {"transaction_id": "T001", "amount": 500, "date": "2024-09-10"},
                {"transaction_id": "T002", "amount": 1500, "date": "2024-09-15"},
                {"transaction_id": "T003", "amount": 200, "date": "2024-09-20"},
                {"transaction_id": "T004", "amount": 800, "date": "2024-09-21"},
                {"transaction_id": "T005", "amount": 300, "date": "2024-09-22"}
                ]
            },
            "+************": {
                "account_id": "A002",
                "bank_name": "ICICI Bank",
                "customer_name": "Nikesh Samaiya",
                "balance": 50000,   
                "transactions": [
                {"transaction_id": "T001", "amount": 500, "date": "2024-09-10"},
                {"transaction_id": "T002", "amount": 1500, "date": "2024-09-15"},
                {"transaction_id": "T003", "amount": 200, "date": "2024-09-20"},
                {"transaction_id": "T004", "amount": 800, "date": "2024-09-21"},
                {"transaction_id": "T005", "amount": 300, "date": "2024-09-22"}
                ]
            }
        }
        
        # Set user data to instance variables
        user_data = bank_data.get(self.mobile)
        if user_data:
            self.account_id = user_data["account_id"]
            self.bank_name = user_data["bank_name"]
            self.customer_name = user_data["customer_name"]
            self.balance = user_data["balance"]
            self.transactions = user_data["transactions"]

    def get_recent_transactions(self) -> List[Dict]:
        """Returns the customer's last 5 transactions."""
        return self.transactions[-5:]  # Return last 5 transactions
    
    def get_recent_transactions_summary(self) -> str:
        """Returns a summary of the customer's last 5 transactions as a formatted string."""
        return "\n".join(
            [f"Transaction ID: {tx['transaction_id']}, Amount: Rupees {tx['amount']}, Date: {tx['date']}" for tx in self.get_recent_transactions()]
        )
    
    def get_account_balance(self) -> dict:
        """Returns the current balance for the user's account."""
        return {
          "balance": self.balance
        }

    def get_transaction_history(self) -> List[Dict]:
        """Returns the transaction history for a given account ID."""
        return self.transactions
    
    def validate_transaction(self, transaction_id: str) -> bool:
        """Checks if the transaction ID exists in the user's transaction history."""
        return any(tx["transaction_id"] == transaction_id for tx in self.get_transaction_history())


    def create_support_ticket(self, case_type: str, subject: str, description: str) -> dict:
        """Creates a support ticket (e.g., query, request, complaint, feedback) in the database and returns a confirmation."""
        case_id = db.insert_support_ticket(self.account_id, self.customer_name, case_type, subject, description)
        return {
        "case_id": case_id,
        "status": "success",        
        }


class BankAssistant:
    """AI-powered assistant class to handle banking services, such as balance inquiries and transaction history."""
    
    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None, target: str):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-4o-mini"  # "gpt-3.5-turbo" "gpt-4o" "gpt-4o-mini"
        self.mobile = mobile
        self.client = openai
        self.bank_manager = BankAccountManager(mobile)  # Initialize account manager with user's mobile number
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to {self.bank_manager.bank_name}! How can I assist you today?"
        self.functions = self._create_functions()  # Initialize the functions

    def _create_functions(self) -> List[Dict]:
        """Define the functions to handle balance inquiries, transaction history requests, and support ticket creation."""
        return [
            {
                "name": "manage_account",
                "description": "Handles balance inquiries, transaction history requests, and support ticket creation by inferring details from the user's input.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "operation": {
                            "type": "string",
                            "enum": ["balance_inquiry", "transaction_history", "create_support_ticket"],
                            "description": "The type of operation inferred from the user's request."
                        },
                        "ticket_type": {
                            "type": "string",
                            "enum": ["query", "request", "complaint", "feedback"],
                            "description": "The inferred type of support ticket."
                        },
                        "subject": {
                            "type": "string",
                            "description": "The inferred subject of the support ticket."
                        },
                        "description": {
                            "type": "string",
                            "description": "The detailed description, as inferred from the user's input."
                        },
                        "confirmed": {
                            "type": "boolean",
                            "description": "Indicates whether the user has confirmed the description."
                        }
                        
                    },
                    "required": ["operation"],
                    "if": {
                        "properties": {"operation": {"const": "create_support_ticket"}}
                    },
                    "then": {
                        "required": ["ticket_type", "subject", "description", "confirmed"]
                    }
                }
            }
        ]

    def handle_response(self, assistant_response: ChatCompletion):
        """Processes the assistant's response and takes necessary actions."""
        function_call = assistant_response.choices[0].message.function_call
        if function_call and function_call.name == "manage_account":
            self.conversation_history.append(assistant_response.choices[0].message)
            
            arguments = json.loads(function_call.arguments)
            operation = arguments.get("operation")
            ticket_type = arguments.get("ticket_type", "")
            subject = arguments.get("subject", "")
            description = arguments.get("description", "")
            confirmed = arguments.get("confirmed", False)

            if operation == "balance_inquiry":
                balance = self.bank_manager.get_account_balance()
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": str(balance)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)
                # return f"The current balance for your account is ${balance:.2f}."

            elif operation == "transaction_history":
                transactions = self.bank_manager.get_transaction_history()
                """
                 transaction_details = "\n".join(
                    [f"Date: {txn['date']}, Amount: ${txn['amount']}" for txn in transactions]
                )
                """
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": str(transactions)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)
                # return f"Transaction history:\n{transaction_details}"

            elif operation == "create_support_ticket":
                # Validate inputs for creating a support ticket
                if not ticket_type or not subject or not description:
                    return "Please provide the ticket type, subject, and description for creating a support ticket."

                # Create the support ticket and provide confirmation
                ticket_response = self.bank_manager.create_support_ticket(ticket_type, subject, description)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": str(ticket_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)
                # return ticket_response

        return assistant_response.choices[0].message.content

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for banking services."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)


    def query(self, sentence: str, context: str) -> str:
        """Handles user queries such as balance inquiries and transaction history."""
        self.conversation_history.append(self._create_message("user", sentence))

        messages_for_llm = self.conversation_history[-12:]
        system_message = self._create_message("system", self._generate_initial_content())
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message

        response, _ = self._get_chat_response_with_timing(messages_for_llm)
        if response:
            self.conversation_history.append(self._create_message("assistant", response))
        return response

    def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        """Gets a response from the GPT model and tracks the time taken."""
        try:
            start_time = time.time()
            chat_response = self._get_chat_response(messages)
            end_time = time.time()
            response_time = end_time - start_time
        except OpenAIError as error:
            self.logger.error(error)
            return "Sorry, I can't process your request right now.", 0
        
        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with available accounts and bank name."""
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        base_instructions = (
            f'You are a bank assistant named "Lisa", providing banking services for {self.bank_manager.bank_name}. '
            "You are a professional and courteous customer care representative assisting users via voice call. "
            "The user is a non-native English speaker, and the transcribed input may contain errors due to mispronunciations. "
            "Please interpret the user's input carefully, considering possible mispronunciations, and seek clarification if needed. "
            "Respond only in English. Your responses will be audio-fed, so keep them concise within 1 to 2 sentences without any parentheses.\n"
            f"Today's date and time is {current_date_time}.\n\n"
        )

        account_details = f"Account Holder: {self.bank_manager.customer_name}, Balance: Rupees {self.bank_manager.balance}"
        account_info = (
            f"Here are the user account:\n{account_details}\n\n" if account_details else "No user account available.\n\n"
        )

        # Provide recent transactions if available
        recent_transactions = self.bank_manager.get_recent_transactions_summary()
        transactions_info = (
            f"Here are the Recent Transactions:\n{recent_transactions}\n\n" if recent_transactions else "No recent transactions available.\n\n"
        )
        
        # Common instructions for handling out-of-scope queries
        common_instructions = (
            "- Keep the conversation short and simple.\n"
            "- Provide feedback if any information is missing or needs clarification. Respond clearly if the user's input is out of scope.\n"
        )

        specific_instructions = (                
            "\n\nInstructions:\n"
            "- Assist users with balance inquiries, transaction history requests, and support ticket creation.\n"
            "- Infer the user's intended operation (balance inquiry, transaction history, create support ticket) from their input without asking them directly.\n"
            "- If creating a support ticket:\n"
            "   - Analyze the user's message to determine the ticket type (query, request, complaint, feedback).\n"
            "   - Create an appropriate subject that summarizes the user's input.\n"
            "   - Generate a detailed description based on the user's input.\n"
        )
        
        # Combine all parts to form the final prompt
        prompt = (
            f"{base_instructions}"
            f"{account_info}"
            f"{transactions_info}"
            f"{specific_instructions}"
            f"{common_instructions}"
        )

        return prompt

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}
    
    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)
