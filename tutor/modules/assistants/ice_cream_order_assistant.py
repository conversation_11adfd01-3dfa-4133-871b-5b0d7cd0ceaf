import json
import os
import re
import time
from threading import Thread
from typing import <PERSON><PERSON>, Tu<PERSON>, List

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai import OpenAI
from openai.types.chat.chat_completion import ChatCompletion

from tutor.executors import static_responses, files
from tutor.executors.static_responses import un_processable
from tutor.modules.exceptions import MissingEnvVars

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

class IceCreamOrderAssistant:
    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-3.5-turbo"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}.yaml"
        self.mobile = mobile
        self.client = OpenAI()
        self.functions = self._create_functions()
        self.menu = self._initialize_menu()
        self.special_offers = self._initialize_special_offers()
        self.session = session
        self.delivery_address = None  # Stores the delivery address
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to our ice cream shop! How can I assist you today?"

    def _initialize_menu(self) -> dict:
        """Initializes the menu with flavors, toppings, and prices."""
        return {
            "flavors": [
                {"flavorName": "Strawberry", "count": 50, "price": 90},
                {"flavorName": "Chocolate", "count": 75, "price": 160}
            ],
            "toppings": [
                {"toppingName": "Hot Fudge", "count": 50, "price": 150},
                {"toppingName": "Sprinkles", "count": 2000, "price": 75},
                {"toppingName": "Whipped Cream", "count": 50, "price": 100}
            ]
        }

    def _initialize_special_offers(self) -> dict:
        """Initializes special offers."""
        return {
            "offers": [
                {"offerName": "Winter Wonderland Discount",
                 "details": "25% off on all orders above Rs200 during the winter season."},
                {"offerName": "Two for Tuesday", 
                 "details": "Buy one get one free on all ice cream flavors every Tuesday."},
            ]
        }

    def _create_functions(self):
        """Define the functions to handle food ordering process."""
        return [
            {
                "name": "place_order",
                "description": "Takes the customer’s order and processes it, including delivery address.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "flavors": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of flavors the customer wants to order."
                        },
                        "toppings": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of toppings the customer wants to add."
                        },
                        "delivery_address": {
                            "type": "string",
                            "description": "The address where the order should be delivered."
                        }
                    },
                    "required": ["flavors", "toppings", "delivery_address"]
                }
            }
        ]

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise MissingEnvVars
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for food ordering."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            chat_response = self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    def query(self, sentence: str, context: str) -> str:
        response_time = 0.0
        sentence = sentence.lower()

        self.conversation_history.append(self._create_message("user", sentence))

        # Prepare messages for the LLM
        messages_for_llm = self.conversation_history[-12:]  # Last 6 messages to keep within token limits

        self.english_context = context
        system_message = self._create_message("system", self._generate_initial_content())
        messages_for_llm.insert(0, system_message)

        response, response_time = self._get_chat_response_with_timing(messages_for_llm)

        if response:
            self.conversation_history.append(self._create_message("assistant", response))

        return response

    def _get_chat_response_with_timing(self, messages) -> tuple:
        try:
            start_time = time.time()  # Start timing
            chat_response = self._get_chat_response(messages)
            end_time = time.time()  # End timing
            response_time = end_time - start_time  # Calculate the duration
        except OpenAIError as error:
            self.logger.error(error)
            return un_processable("Customer"), '0'

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return un_processable("Customer"), '0'

    def handle_response(self, assistant_response: ChatCompletion):
        function_call = assistant_response.choices[0].message.function_call

        if function_call and function_call.name == "place_order":
            flavors = json.loads(function_call.arguments).get("flavors", [])
            toppings = json.loads(function_call.arguments).get("toppings", [])
            self.delivery_address = json.loads(function_call.arguments).get("delivery_address")
            return self._process_order(flavors, toppings)

        return assistant_response.choices[0].message.content

    def _process_order(self, flavors: List[str], toppings: List[str]) -> str:
        """Processes the order, checks availability, calculates total price, and confirms the order."""
        unavailable_flavors = [flavor for flavor in flavors if not self._is_item_available(flavor, "flavors")]
        unavailable_toppings = [topping for topping in toppings if not self._is_item_available(topping, "toppings")]

        if unavailable_flavors or unavailable_toppings:
            unavailable_message = ""
            if unavailable_flavors:
                unavailable_message += f"The following flavors are unavailable: {', '.join(unavailable_flavors)}. "
            if unavailable_toppings:
                unavailable_message += f"The following toppings are unavailable: {', '.join(unavailable_toppings)}."
            return unavailable_message

        # Calculate total price
        total_price = self._calculate_price(flavors, toppings)

        return (f"Your order for flavors: {', '.join(flavors)} with toppings: {', '.join(toppings)} "
                f"has been placed successfully. The total price is Rs{total_price:.2f}. "
                f"The order will be delivered to: {self.delivery_address}.")

    def _calculate_price(self, flavors: List[str], toppings: List[str]) -> float:
        """Calculates the total price for the selected flavors and toppings."""
        total = 0.0
        for flavor in flavors:
            for item in self.menu["flavors"]:
                if item["flavorName"].lower() == flavor.lower():
                    total += item["price"]

        for topping in toppings:
            for item in self.menu["toppings"]:
                if item["toppingName"].lower() == topping.lower():
                    total += item["price"]

        return total

    def _is_item_available(self, item_name: str, category: str) -> bool:
        """Checks if the item (flavor/topping) is available in stock."""
        for item in self.menu[category]:
            if item_name.lower() == item.get(f"{category[:-1]}Name").lower() and item["count"] > 0:
                return True
        return False

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with the menu, prices, and special offers."""
        flavors = [f"{item['flavorName']} (${item['price']}, Available: {item['count']})" for item in self.menu["flavors"]]
        toppings = [f"{item['toppingName']} (${item['price']}, Available: {item['count']})" for item in self.menu["toppings"]]
        offers = [f"{offer['offerName']}: {offer['details']}" for offer in self.special_offers["offers"]]

        menu_details = f"Flavors:\n{', '.join(flavors)}\nToppings:\n{', '.join(toppings)}" 
        special_offer_details = "\nSpecial Offers:\n" + "\n".join(offers)

        return (            
            'You are an ice cream assistant chatbot named "Pooja"'
            "You are a helpful customer care representative connecting with users via voice call. "
            "Your responses will be audio-fed, so keep them concise within 1 to 2 sentences without any parentheses. "
            "Your expertise is exclusively in providing information and advice about anything related to ice creams."
            "This includes flavor combinations, ice cream recipes, and general ice cream-related queries."
            "You do not provide information outside of this scope.\n\n"            
            "Here is the current menu and offers.\n\n"
            f"{menu_details}\n\n{special_offer_details}\n\n"
            "Once the user confirms their flavors and toppings, ask them for their delivery address to complete the order."
            "Mention special offers only if the user asks about them or shows interest."
        )

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)
