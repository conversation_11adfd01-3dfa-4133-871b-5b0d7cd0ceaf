import os
import json
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import openai
import logging
from openai import OpenAIError

class QAParameter:
    def __init__(self, id: int, name: str, max_score: int, parent_id: Optional[int] = None):
        self.id = id
        self.name = name
        self.max_score = max_score
        self.parent_id = parent_id

class QAAssistant:
    """Assistant class for analyzing agent performance in customer calls and generating QA scores."""

    # Static list of QA parameters with their maximum scores
    parameters: List[QAParameter] = [
        # Main categories (ParentId = None)
        QAParameter(1, "Call Opening", 24, None),
        QAParameter(2, "Voice / Communication / Soft Skills", 37, None),
        QAParameter(3, "Probing Skills", 6, None),
        QAParameter(4, "Product Knowledge", 8, None),
        QAParameter(5, "FTR (First Time Resolution)", 12, None),
        QAParameter(6, "Call Logging", 7, None),
        QAParameter(7, "Proactive Information and Closing", 6, None),

        # Sub-parameters with ParentId set to the respective main category Id
        # Call Opening
        QAParameter(101, "Welcome Script (Opening within 3 seconds)", 2, 1),
        QAParameter(102, "Authentication / Security Check", 8, 1),
        QAParameter(103, "Personalization (Using 'Sir/Madam' appropriately)", 2, 1),
        QAParameter(104, "CSR Understanding the Customer Query", 4, 1),
        QAParameter(105, "Avoiding Unnecessary Interruptions", 4, 1),
        QAParameter(106, "Acknowledging the Customer", 2, 1),
        QAParameter(107, "Avoiding Dead Air", 2, 1),

        # Voice / Communication / Soft Skills
        QAParameter(201, "Tone, Pitch, and Clarity", 6, 2),
        QAParameter(202, "Correct Pronunciation, Grammar, and Language Use", 6, 2),
        QAParameter(203, "Empathy / Apology for Inconvenience", 5, 2),
        QAParameter(204, "Professionalism (No rudeness, disconnection, or sarcasm)", 8, 2),
        QAParameter(205, "Not Rushing the Call", 4, 2),
        QAParameter(206, "Courtesy and Willingness to Help", 4, 2),
        QAParameter(207, "Objection Handling and Call Control", 4, 2),

        # Probing Skills
        QAParameter(301, "Asking Proper and Relevant Questions", 6, 3),

        # Product Knowledge
        QAParameter(401, "Providing Complete and Accurate Information, TAT, and Impact Tagging", 8, 4),

        # FTR (First Time Resolution)
        QAParameter(501, "No Unnecessary Hold (Max 1 minute)", 4, 5),
        QAParameter(502, "Hold Protocol Followed (Permission and Thank You)", 2, 5),
        QAParameter(503, "System Check Performed", 4, 5),
        QAParameter(504, "Escalation Procedure Followed", 2, 5),

        # Call Logging
        QAParameter(601, "Correct Log Entry with Relevant Comments", 5, 6),
        QAParameter(602, "Request/Reference Number Provided", 2, 6),

        # Proactive Information and Closing
        QAParameter(701, "Information About Missed Calls Provided", 2, 7),
        QAParameter(702, "Offered Additional Assistance", 2, 7),
        QAParameter(703, "Proper Call Closing", 2, 7)
    ]

    def __init__(self, logger, transcript: Dict, db):
        self.logger = logger
        self.transcript = transcript  # Transcript dictionary with utterances
        self.db = db  # Database instance for storing QA results
        self.model = "gpt-4o"  # Using a more capable model for detailed QA analysis
        self.client = openai
        self.qa_history = []  # Stores QA analysis history
        self.functions = self._create_functions()

    def _create_functions(self) -> List[Dict]:
        """Define the function for analyzing agent performance based on pre-defined QA parameters."""
        # Create a dictionary of sub-parameters grouped by main parameter
        parameter_groups = {}
        for param in self.parameters:
            if param.parent_id is None:
                parameter_groups[param.id] = {
                    "name": param.name,
                    "max_score": param.max_score,
                    "sub_parameters": []
                }
        
        for param in self.parameters:
            if param.parent_id is not None:
                parameter_groups[param.parent_id]["sub_parameters"].append({
                    "id": param.id,
                    "name": param.name,
                    "max_score": param.max_score
                })

        # Create the function definition
        return [
            {
                "name": "analyze_agent_performance",
                "description": "Analyzes agent performance in a customer call based on QA parameters and generates scores.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "call_summary": {
                            "type": "string",
                            "description": "A brief summary of the call content and context."
                        },
                        "agent_strengths": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of agent's strengths observed in the call."
                        },
                        "agent_areas_for_improvement": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of areas where the agent can improve."
                        },
                        "parameter_scores": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "parameter_id": {
                                        "type": "integer",
                                        "description": "ID of the QA parameter being scored."
                                    },
                                    "score": {
                                        "type": "number",
                                        "description": "Score assigned to this parameter (should not exceed max_score)."
                                    },
                                    "comments": {
                                        "type": "string",
                                        "description": "Specific comments or observations related to this parameter."
                                    }
                                },
                                "required": ["parameter_id", "score"]
                            },
                            "description": "Scores for each QA parameter."
                        },
                        "total_score": {
                            "type": "number",
                            "description": "Total QA score out of 100."
                        },
                        "call_category": {
                            "type": "string",
                            "enum": ["Excellent", "Good", "Average", "Below Average", "Poor"],
                            "description": "Overall call quality category based on total score."
                        }
                    },
                    "required": ["call_summary", "parameter_scores", "total_score", "call_category"]
                }
            }
        ]

    def analyze_call(self) -> Dict[str, Any]:
        """Analyzes the call transcript and generates QA scores and feedback."""

        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'openai_api_key' wasn't found to proceed")
            return self._generate_empty_result()

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        messages = []
        
        try:
            # Generate the initial content for the assistant
            content = self._generate_initial_content()
            messages.append(self._create_message("system", content))
            
            # Add the transcript for analysis
            transcript_text = self._format_transcript_for_analysis()
            messages.append(self._create_message("user", transcript_text))
            
            # Get the chat response from the OpenAI API
            chat_response = self._get_chat_response(messages)
            
            # Parse the QA analysis from the response
            qa_result = self._parse_qa_analysis(chat_response)
            
            # Store the QA result in the database
            self._store_qa_result(qa_result)
            
            # Log the QA analysis
            self._log_qa_analysis(qa_result)
            
            return qa_result

        except OpenAIError as error:
            self.logger.error(f"QA analysis error: {error}")
            return self._generate_empty_result()

    def _generate_empty_result(self) -> Dict[str, Any]:
        """Generates an empty result when analysis fails."""
        return {
            "call_summary": "",
            "agent_strengths": [],
            "agent_areas_for_improvement": [],
            "parameter_scores": [],
            "total_score": 0,
            "call_category": "Poor",
            "error": "Failed to perform QA analysis"
        }

    def _format_transcript_for_analysis(self) -> str:
        """Formats the transcript into a readable format for analysis."""
        formatted_text = "Call Transcript for QA Analysis:\n\n"
        
        if "utterances" in self.transcript and self.transcript["utterances"]:
            for utterance in self.transcript["utterances"]:
                speaker = utterance.get("speaker", "Unknown")
                text = utterance.get("text", "")
                formatted_text += f"{speaker}: {text}\n\n"
        else:
            formatted_text += f"Full Transcript: {self.transcript.get('transcript_text', '')}\n\n"
            
        return formatted_text

    def _parse_qa_analysis(self, response) -> Dict[str, Any]:
        """Extracts the QA analysis results from the API response."""
        if response.choices and response.choices[0].message.function_call:
            function_call = response.choices[0].message.function_call
            if function_call.name == "analyze_agent_performance":
                try:
                    result = json.loads(function_call.arguments)
                    
                    # Validate and ensure all required fields are present
                    if not all(key in result for key in ["call_summary", "parameter_scores", "total_score", "call_category"]):
                        self.logger.warning("Incomplete QA analysis result")
                        return self._generate_empty_result()
                    
                    # Ensure agent_strengths and agent_areas_for_improvement are lists
                    if "agent_strengths" not in result:
                        result["agent_strengths"] = []
                    if "agent_areas_for_improvement" not in result:
                        result["agent_areas_for_improvement"] = []
                    
                    # Add timestamp to the result
                    result["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    return result
                except json.JSONDecodeError:
                    self.logger.error("Failed to parse QA analysis result")
                    return self._generate_empty_result()
        
        self.logger.warning("No valid QA analysis result found in response")
        return self._generate_empty_result()

    def _store_qa_result(self, qa_result: Dict[str, Any]) -> None:
        """Stores the QA result in the database."""
        try:
            # Generate a unique QA report ID
            qa_report_id = self._generate_qa_report_id()
            
            # Add the report ID to the result
            qa_result["report_id"] = qa_report_id
            
            # Store the main QA report
            self._store_qa_report(qa_report_id, qa_result)
            
            # Store individual parameter scores
            self._store_parameter_scores(qa_report_id, qa_result["parameter_scores"])
            
            self.logger.info(f"QA result stored successfully with ID: {qa_report_id}")
        except Exception as e:
            self.logger.error(f"Failed to store QA result: {str(e)}")

    def _generate_qa_report_id(self) -> str:
        """Generates a unique QA report ID."""
        from tutor.modules.utils import shared
        shared.seq_number += 1
        timestamp = datetime.now().strftime("%d%H%M%S%f")[:9]  # Format: ddHHmmsss
        return f"QA{timestamp}{shared.seq_number:03}"

    def _store_qa_report(self, qa_report_id: str, qa_result: Dict[str, Any]) -> None:
        """Stores the main QA report in the database."""
        # Create QAReport table if it doesn't exist
        self._create_qa_report_table()
        
        # Insert the QA report
        query = """
            INSERT INTO QAReport (
                report_id, call_summary, total_score, call_category, 
                timestamp, agent_strengths, agent_areas_for_improvement
            )
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        agent_strengths = ", ".join(qa_result["agent_strengths"])
        agent_areas = ", ".join(qa_result["agent_areas_for_improvement"])
        
        with self.db.connection:
            cursor = self.db.connection.cursor()
            cursor.execute(query, (
                qa_report_id,
                qa_result["call_summary"],
                qa_result["total_score"],
                qa_result["call_category"],
                qa_result["timestamp"],
                agent_strengths,
                agent_areas
            ))
            self.db.connection.commit()

    def _store_parameter_scores(self, qa_report_id: str, parameter_scores: List[Dict]) -> None:
        """Stores the individual parameter scores in the database."""
        # Create QAParameterScore table if it doesn't exist
        self._create_qa_parameter_score_table()
        
        # Insert each parameter score
        query = """
            INSERT INTO QAParameterScore (
                report_id, parameter_id, score, comments
            )
            VALUES (?, ?, ?, ?)
        """
        
        with self.db.connection:
            cursor = self.db.connection.cursor()
            for score in parameter_scores:
                cursor.execute(query, (
                    qa_report_id,
                    score["parameter_id"],
                    score["score"],
                    score.get("comments", "")
                ))
            self.db.connection.commit()

    def _create_qa_report_table(self) -> None:
        """Creates the QAReport table if it doesn't exist."""
        columns = [
            "report_id TEXT PRIMARY KEY",
            "call_summary TEXT",
            "total_score REAL",
            "call_category TEXT",
            "timestamp TEXT",
            "agent_strengths TEXT",
            "agent_areas_for_improvement TEXT"
        ]
        self.db.create_table("QAReport", columns)

    def _create_qa_parameter_score_table(self) -> None:
        """Creates the QAParameterScore table if it doesn't exist."""
        columns = [
            "id INTEGER PRIMARY KEY AUTOINCREMENT",
            "report_id TEXT",
            "parameter_id INTEGER",
            "score REAL",
            "comments TEXT",
            "FOREIGN KEY (report_id) REFERENCES QAReport (report_id)"
        ]
        self.db.create_table("QAParameterScore", columns)

    def _log_qa_analysis(self, qa_result: Dict[str, Any]) -> None:
        """Logs the QA analysis for record-keeping."""
        self.qa_history.append(qa_result)
        self.logger.info(
            f"QA analysis logged: Total Score={qa_result['total_score']}, "
            f"Category='{qa_result['call_category']}'"
        )

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with detailed instructions for QA analysis."""
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Create a formatted list of parameters and sub-parameters with their max scores
        parameters_text = ""
        for param in self.parameters:
            if param.parent_id is None:
                parameters_text += f"\n{param.id}. {param.name} (Max Score: {param.max_score})\n"
                # Add sub-parameters
                for sub_param in self.parameters:
                    if sub_param.parent_id == param.id:
                        parameters_text += f"   - {sub_param.name} (ID: {sub_param.id}, Max Score: {sub_param.max_score})\n"

        base_instructions = (
            "You are a Quality Analysis (QA) assistant that evaluates agent performance in customer service calls. "
            "Your task is to analyze the transcript and score the agent based on predefined QA parameters.\n"
            f"Current date and time: {current_date_time}.\n\n"
            "QA Parameters and Maximum Scores:"
            f"{parameters_text}\n"
            "Instructions:\n"
            "1. Carefully analyze the entire call transcript.\n"
            "2. Score each parameter based on the agent's performance, not exceeding the maximum score for each parameter.\n"
            "3. Provide specific comments for each parameter to justify the score.\n"
            "4. Calculate the total score (out of 100).\n"
            "5. Categorize the call quality based on the total score:\n"
            "   - Excellent: 90-100\n"
            "   - Good: 80-89\n"
            "   - Average: 70-79\n"
            "   - Below Average: 60-69\n"
            "   - Poor: Below 60\n"
            "6. Identify the agent's strengths and areas for improvement.\n"
            "7. Provide a brief summary of the call content and context.\n"
        )

        return base_instructions
    
    def _get_chat_response(self, messages):
        """Gets the chat response from the OpenAI API."""
        if self.functions:
            response = self.client.chat.completions.create(
                messages=messages,
                model=self.model,
                temperature=0.2,
                max_tokens=2000,
                functions=self.functions,
                function_call="auto",
            )

        return response
        
    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}
