import os
import json
from typing import List, Dict, <PERSON><PERSON>
from datetime import datetime
import openai
import logging
from openai import OpenAIError

class SentimentAnalyzeAssistant:
    """Assistant class for analyzing sentiment in customer interactions using detailed sentiment categories."""

    def __init__(self, logger, record: Dict, db):
        self.logger = logger
        self.record = record  # Single record dictionary passed in for analysis
        self.db = db  # Database instance for updating sentiment in the record
        self.model = "gpt-4o-mini"  # Specify the model for sentiment analysis
        self.client = openai
        self.sentiment_history = []  # Stores analyzed sentiments
        self.functions = self._create_functions()

    def _create_functions(self) -> List[Dict]:
        """Define the function for analyzing detailed customer sentiment based on pre-defined categories."""
        return [
            {
                "name": "analyze_sentiment",
                "description": "Analyzes the sentiment of a text, categorizes it, assigns a score, and identifies key emotions.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "text": {
                            "type": "string",
                            "description": "The text to analyze for sentiment.",
                        },
                        "sentiment_category": {
                            "type": "string",
                            "enum": [
                                "Confusion", "Customer Waiting", "Frustration", "Impatience", "Negative", "Not Helpful", 
                                "Profanity", "Slightly Negative", "Very Negative", "Neutral", "Fast Response", "Good Information", 
                                "Great Support", "Helpful", "Positive", "Slightly Positive", "Very Positive", "Critical Issue", 
                                "Production Issue", "Urgency", "Call Request", "Follow-up Request", "Churn Risk", "Feature Request", 
                                "Negative Feedback", "Positive Feedback", "Usability Issue", "Escalation Request", "Root Cause", 
                                "Documentation", "Log Message", "Error Message", "Command Output"
                            ],
                            "description": "The sentiment category derived from the text."
                        },
                        "sentiment_score": {
                            "type": "number",
                            "description": "The sentiment score ranging from 0 (very negative) to 100 (very positive).",
                        },
                        "key_emotions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Key emotions associated with the sentiment (e.g., joy, sadness, anger, surprise)."
                        }
                    },
                    "required": ["text"]
                }
            }
        ]

    def analyze_sentiment(self) -> Tuple[str, str, int, List[str]]:
        """Analyzes the sentiment of the record's description text, categorizes it, assigns a score, and identifies key emotions."""

        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        messages = []
        
        try:

            content = self._generate_initial_content()
            messages.append(self._create_message("system", content))
            text = self.record.get("description", "")
            messages.append(self._create_message("user", text))
            chat_response = self._get_chat_response(messages)
        
            text_sentiment, sentiment, score, emotions = self._parse_sentiment(chat_response)
            self._log_sentiment_analysis(text, sentiment, score, emotions)
            return text_sentiment, sentiment, score, emotions
        except OpenAIError as error:
            self.logger.error(f"Sentiment analysis error: {error}")
            return "Neutral", 50, []

    def _parse_sentiment(self, response) -> Tuple[str, str, int, List[str]]:
        """Extracts the sentiment category, score, and key emotions from the response."""
        if response.choices and response.choices[0].message.function_call:
            function_call = response.choices[0].message.function_call
            if function_call.name == "analyze_sentiment":
                result = json.loads(function_call.arguments)
                text_sentiment = result.get("text", "")
                sentiment_category = result.get("sentiment_category", "Neutral")
                sentiment_score = result.get("sentiment_score", 50)
                key_emotions = result.get("key_emotions", [])
                return text_sentiment, sentiment_category, sentiment_score, key_emotions
        return "", "Neutral", 50, []



    def _log_sentiment_analysis(self, text: str, sentiment: str, score: int, emotions: List[str]) -> None:
        """Logs each sentiment analysis to a history list for record-keeping."""
        self.sentiment_history.append({
            "text": text,
            "sentiment": sentiment,
            "score": score,
            "emotions": emotions,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with detailed instructions for sentiment analysis."""
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        base_instructions = (
            "You are a sentiment analysis assistant that categorizes customer feedback "
            "using predefined sentiment categories and assigns a score from 0 to 100.\n"
            f"Current date and time: {current_date_time}.\n"
            "Sentiment Categories:\n"
            "- Negative: Confusion, Customer Waiting, Frustration, Impatience, Negative, Not Helpful, Profanity, "
            "Slightly Negative, Very Negative\n"
            "- Positive: Fast Response, Good Information, Great Support, Helpful, Positive, Slightly Positive, Very Positive\n"
            "- Need Attention: Critical Issue, Production Issue, Urgency\n"
            "- Customer Request: Call Request, Follow-up Request\n"
            "- Product Feedback: Churn Risk, Feature Request, Negative Feedback, Positive Feedback, Usability Issue\n"
            "- Escalation Activity: Escalation Request\n"
            "- Other: Root Cause, Documentation, Log Message, Error Message, Command Output\n\n"
            "Instructions:\n"
            "- Assign a score between 0 (very negative) and 100 (very positive) based on the sentiment.\n"
            "- Identify key emotions such as joy, sadness, anger, and surprise if present.\n"
            "- Carefully read each message and tag it with the most relevant category.\n"
            "- If unsure, assign 'Neutral' with a score of 50.\n"
        )

        return base_instructions
    
    def _get_chat_response(self, messages):
        """Gets the chat response from the OpenAI API and prints the response time."""
        if self.functions:
            response = self.client.chat.completions.create(
                messages=messages,
                model=self.model,
                temperature=0.2,
                max_tokens=300,
                functions=self.functions,
                function_call="auto",
            )

        return response
        
    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}