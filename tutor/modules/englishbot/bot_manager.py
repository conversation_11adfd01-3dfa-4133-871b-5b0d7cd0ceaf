from typing import Tu<PERSON>, Optional, Dict, Union

import requests

from tutor.executors import static_responses
from tutor.modules.models import models
from tutor.modules import logger
from tutor.modules.englishbot.bot import EnglishTutorAssistant
from tutor.modules.firebase import chatting


def _validate_greeting(greeting: str) -> str:
    unwanted_phrases = ["output completed", "output: completed", "hello", "hi there"]
    for phrase in unwanted_phrases:
        greeting = greeting.replace(phrase, "")
    return greeting


def _extract_first_name(full_name: str) -> str:
    """Extracts the first name from the full name."""
    return full_name.split()[0] if full_name else "Student"


def _answer_validate(answer) -> str:
    return answer

def send_message(chat_id: int, response: str, parse_mode: Union[str, None] = 'markdown',
                 retry: bool = False) -> requests.Response:
    """Generates a payload to reply to a message received.

    Args:
        chat_id: Chat ID.
        response: Message to be sent to the user.
        parse_mode: Parse mode. Defaults to ``markdown``
        retry: Retry reply in case reply failed because of parsing.

    Returns:
        Response:
        Response class.
    """
    result = chatting.send_message_to_user(response, chat_id)
    if result.status_code == 400 and parse_mode and not retry:  # Retry with response as plain text
        logger.warning("Retrying response as plain text with no parsing")
        send_message(chat_id=chat_id, response=response, parse_mode=None, retry=True)
    return result

class BotManager:
    def __init__(self, user, logger, event_loop):
        self.user = user
        self.logger = logger
        self.tutor_bot = EnglishTutorAssistant(mobile=self.user.mobile, session=self.user.session, logger=logger,full_name=user.name,loop=event_loop)  # Replaced TutorChatbot with EnglishTutorAssistant
        self.full_name = user.name
        self.first_name = _extract_first_name(self.full_name)
        self.last_bot_selected = None

    def start_new_session(self) -> str:
        return self._greeting_message()

    async def handle_user_input(self, user_input: str = None) -> str:
        """Handles user input and returns the assistant's response.

        Args:
            user_input: The input text from the user.

        Returns:
            The response text from the assistant.
        """
        if not user_input:
            return "Sorry, I didn't get that. Please try again."
        answer = await self._query(user_input.lower())
        answer = _answer_validate(answer)
        if not answer:
            answer = "Sorry, I didn't get that."

        # send_message(self.user.mobile, answer, None)
        return answer

    def _greeting_message(self) -> str:
        greeting = f"{static_responses.greeting()}, Welcome to your English tutor session!"
        greeting += static_responses.get_first_response()
        self.tutor_bot.start_new_session()
        self.sentence_validator.start_new_session()
        return greeting

    async def _query(self, phrase: str) -> str:
       return self.tutor_bot.query(phrase,"")

    async def _handle_tutor_query(self, phrase: str) -> Tuple[bool, str]:
        """
        Handles queries related to English tutoring FAQs.

        :param phrase: The query phrase related to English tutoring.
        :return: A tuple containing a boolean indicating if the source matches "tutor_faqs.csv"
                 and the answer from the TutorFaqsService instance.
        """
        faq_response = await self.tutor_faqs_service.get_best_match(phrase)
        if faq_response:
            answer_parts = faq_response.answer.split("answer:")
            answer = answer_parts[-1].strip() if len(answer_parts) > 1 else faq_response.answer
            return faq_response.is_source_tutor_faqs("tutor_faqs.csv"), answer
        else:
            return False, "No answer available."

    def process_llm_answer(self, llm_answer: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        return self.sentence_validator.process_llm_answer(llm_answer)

    def post_sentence_correction(self, ai_response: str, result: Optional[Dict]) -> str:
        return self.sentence_validator.post_sentence_correction(ai_response, result)

    def handle_no_sentence_case(self, llm_answer: str) -> Tuple[Optional[str], bool]:
        return self.sentence_validator.handle_no_sentence_case(llm_answer)

    def format_sentence(self, sentence: str) -> str:
        return self.sentence_validator.format_sentence(sentence)
