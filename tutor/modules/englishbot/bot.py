import json
import os
import re
import time
from threading import Thread
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List

import openai
from dotenv import load_dotenv
from fuzzywuzzy import process
# from langchain.llms import openai
from openai import OpenAIError
from openai import OpenAI
from openai.types.chat.chat_completion import Cha<PERSON><PERSON><PERSON>pletion

from tutor.executors import static_responses, files
from tutor.executors.static_responses import un_processable
from tutor.modules.exceptions import MissingEnvVars
from tutor.modules.text import word_to_number, amount
from tutor.modules.utils import util

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

class EnglishTutorAssistant:
    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-3.5-turbo"  # "gpt-3.5-turbo" "gpt-4o"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}.yaml"
        self.mobile = mobile
        self.client = OpenAI()
        self.functions = None #self._create_functions()
        self.english_context = ""
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, I'm here to help you improve your English! How can I assist you today?"

    def _create_functions(self):
        """Define the function to process and correct the English sentences."""
        return [
            {
                "name": "correct_sentence",
                "description": "Corrects the provided sentence for grammar, punctuation, or phrasing improvements.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "sentence": {
                            "type": "string",
                            "description": "The sentence to be corrected."
                        },
                        "suggestion": {
                            "type": "string",
                            "description": "The suggested correction."
                        }
                    },
                    "required": ["sentence", "suggestion"]
                }
            }
        ]

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Student"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise MissingEnvVars
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for English tutoring."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            chat_response = self._get_chat_response(self.conversation_history)
            # self._process_chat_response(chat_response)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    def set_user_level(self, level: str):
        """Sets the user level for the session."""
        if level.lower() in ['beginner', 'intermediate', 'advanced', 'fluency']:
            self.user_level = level.lower()
            self.logger.info(f"User level set to {self.user_level}")
        else:
            self.logger.warning(f"Invalid level provided: {level}. Setting to default 'beginner'")
            self.user_level = "beginner"

    def _generate_initial_content(self) -> str:
        """Generates the initial content based on user level."""
        if self.user_level == "beginner":
            return self._generate_beginner_content()
        elif self.user_level == "intermediate":
            return self._generate_intermediate_content()
        elif self.user_level == "advanced":
            return self._generate_advanced_content()
        elif self.user_level == "fluency":
            return self._generate_fluency_content()
        else:
            self.logger.warning("Invalid user level, defaulting to beginner content.")
            return self._generate_beginner_content()
        
    def query(self, sentence: str, context: str) -> str:
        response_time = 0.0
        sentence = sentence.lower()

        self.conversation_history.append(self._create_message("user", sentence))

        # Prepare messages for the LLM
        messages_for_llm = self.conversation_history[-12:]  # Last 6 messages to keep within token limits

        self.english_context = context
        system_message = self._create_message("system", self._generate_initial_content())
        messages_for_llm.insert(0, system_message)

        response, response_time = self._get_chat_response_with_timing(messages_for_llm)

        if response:
            self.conversation_history.append(self._create_message("assistant", response))
            # Thread(target=self._dump_history, args=(sentence, response, f"{response_time:.2f}")).start()

        return response

    def _get_chat_response_with_timing(self, messages) -> tuple:
        try:
            start_time = time.time()  # Start timing
            chat_response = self._get_chat_response(messages)
            end_time = time.time()  # End timing
            response_time = end_time - start_time  # Calculate the duration
        except OpenAIError as error:
            self.logger.error(error)
            return un_processable("Student"), '0'

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return un_processable("Student"), '0'

    def handle_response(self, assistant_response: ChatCompletion):
        function_call = assistant_response.choices[0].message.function_call

        if function_call and function_call.name == "correct_sentence":
            arguments = json.loads(function_call.arguments)
            sentence = arguments.get("sentence")
            suggestion = arguments.get("suggestion")
            self.logger.info(f"Sentence corrected :{arguments}")
            # Thread(target=self._dump_history, args=("correct_sentence", arguments, "")).start()
            return f"Original: '{sentence}', Suggested correction: '{suggestion}'"
        else:
            return assistant_response.choices[0].message.content.lower()


    def _generate_beginner_content(self) -> str:
        """Generates the initial content for beginner-level English tutoring."""
        base_instructions = (
            "You are an AI English tutor for beginners. "
            "Your goal is to help users learn basic vocabulary and simple sentence structures. "
            "Speak slowly and use short, simple words. Keep your replies within 1 to 2 sentences."
        )

        content = (
            f"{base_instructions}\n"
            "Instructions:\n"
            "- Ask the user to form a simple sentence using basic words like 'I', 'you', 'eat', 'go', etc.\n"
            "- If they make a mistake, correct them and explain why the correction is better.\n"
            "- Encourage them to repeat the correct sentence out loud.\n"
            "- Ask follow-up questions to help them use the new words they’ve learned.\n"
        )

        return content

    def _generate_intermediate_content(self) -> str:
        """Generates the initial content for intermediate-level English tutoring."""
        base_instructions = (
            "You are an AI English tutor for intermediate learners. "
            "Your goal is to help users improve their vocabulary, grammar, and conversational skills. "
            "Provide examples of slightly more complex sentence structures and idioms. Keep your replies within 1 to 2 sentences."
        )

        content = (
            f"{base_instructions}\n"
            "Instructions:\n"
            "- Ask the user to form a sentence using different tenses or more advanced vocabulary.\n"
            "- If they make a mistake, provide a clear correction and explain the grammar rule or reason for the correction.\n"
            "- Encourage the user to practice by forming more sentences using the corrected structure.\n"
            "- Engage them in conversation by asking them to respond to common questions or scenarios, like talking about daily routines.\n"
        )

        return content

    def _generate_advanced_content(self) -> str:
        """Generates the initial content for advanced-level English tutoring."""
        base_instructions = (
            "You are an AI English tutor for advanced learners. "
            "Your goal is to help users refine their fluency, pronunciation, and natural expression. "
            "Encourage the use of idioms, complex sentence structures, and natural-sounding language. Keep your replies concise."
        )

        content = (
            f"{base_instructions}\n"
            "Instructions:\n"
            "- Ask the user to discuss more abstract or complex topics (e.g., culture, technology, etc.).\n"
            "- Offer suggestions for more natural or fluent phrasing and explain why the changes improve clarity or expression.\n"
            "- Provide examples of how to sound more conversational or use idiomatic expressions.\n"
            "- Challenge the user by engaging in deeper conversation, asking follow-up questions and encouraging them to expand their answers.\n"
        )

        return content

    def _generate_fluency_content(self) -> str:
        """Generates the initial content for conversational fluency tutoring."""
        base_instructions = (
            "You are an AI English tutor helping users practice conversational fluency. "
            "The goal is to encourage users to speak confidently and naturally. Focus less on perfection and more on fluid conversation."
        )

        content = (
            f"{base_instructions}\n"
            "Instructions:\n"
            "- Ask the user to respond to a casual conversation starter (e.g., 'How was your day?' or 'What’s your favorite hobby?').\n"
            "- If they pause or struggle, offer suggestions for continuing the conversation naturally.\n"
            "- Encourage them to express their thoughts without worrying too much about grammar.\n"
            "- Engage them by asking more personal or creative questions to keep the conversation going.\n"
        )

        return content

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.7,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            # functions=self.functions,
            # function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)
