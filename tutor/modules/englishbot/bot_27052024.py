import os
from dataclasses import dataclass
from typing import Literal

from dotenv import load_dotenv
from langchain.chains import Conversation<PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.prompts.prompt import PromptTemplate
from langchain_openai import ChatOpenAI

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
@dataclass
class Message:
    """Class for keeping track of interview history."""
    origin: Literal["human", "ai"]
    message: str

start_call_message = "Hello! I am your AI English teacher. Let's practice your spoken English. Please start by introducing yourself."

conversation_history = []
conversation_history.append(
            Message(origin="ai", message=start_call_message)
        )

conversation_memory = ConversationBufferMemory(human_prefix="Student: ", ai_prefix="Teacher")

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0.7)

# Improved prompt template for the AI
PROMPT = PromptTemplate(
    input_variables=["history", "input"],
    template="""You are an AI English teacher, here to help the student practice spoken English. Correct their mistakes and provide explanations where necessary. Ensure your responses are friendly and encouraging. Keep the conversation flowing naturally by asking relevant follow-up questions. Only ask one question at a time.

Current Conversation:
{history}
Student: {input}
Teacher:"""
)

conversation_chain = ConversationChain(prompt=PROMPT, llm=llm, memory=conversation_memory)

def answer_call_back(human_input: str):
    if not human_input:
        input_text = "Sorry, I didn't get that. Please try again."
        #conversation_history.append(Message("ai", input_text))
        return input_text
    else:
        input_text = human_input

    conversation_history.append(Message("human", input_text))
    ai_response = conversation_chain.run(input_text)

    conversation_history.append(Message("ai", ai_response))
    return ai_response