import logging
from typing import Union

import requests
from pydantic import HttpUrl

from tutor.modules.models import models
import firebase_admin
from firebase_admin import credentials, firestore

# Initialize Firebase Admin SDK
cred = credentials.Certificate(models.fileio.english_bot_firebase_Certificate)  # Replace with your service account key file
firebase_admin.initialize_app(cred, {'storageBucket': 'lean-english-4f1ea.appspot.com'})

# Initialize Firestore client
firebase_db = firestore.client()

# Method to get the "url" field from the "webhookinfo" document
def get_url(logger: logging.Logger):
    try:
        doc_ref = firebase_db.collection("configuration").document("webhookinfo")
        doc = doc_ref.get()
        if doc.exists:
            data = doc.to_dict()
            return data #.get("url")
        else:
            return None
    except Exception as e:
        logger.error("Error getting URL:", e)
        return None

# Method to set the "url" field in the "webhookinfo" document
def set_url(base_url: Union[HttpUrl, str], webhook: Union[HttpUrl, str], logger: logging.Logger):
    try:
        doc_ref = firebase_db.collection("configuration").document("webhookinfo")
        doc_ref.set({"url": webhook})
        doc_ref.set({"base_url": base_url})
        logger.info("URL set successfully")
        return "URL set successfully"
    except Exception as e:
        logger.error("Error setting URL:", e)

# Method to delete the "url" field from the "webhookinfo" document
def delete_url(base_url: Union[str, HttpUrl], logger: logging.Logger):
    try:
        doc_ref = firebase_db.collection("configuration").document("webhookinfo")
        doc_ref.update({"url": ""})
        doc_ref.update({"base_url": ""})
        logger.info("URL deleted successfully")
    except Exception as e:
        logger.error("Error deleting URL:", e)


