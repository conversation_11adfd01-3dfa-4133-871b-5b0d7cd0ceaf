from datetime import datetime, timezone

from firebase_admin import storage
from tutor.modules.firebase.configuration import firebase_db
from tutor.modules.logger import logger

current_user_id = "KNZLtEqaZHXLfjq1NiWeP8m88363"

class Response:
    def __init__(self, status_code):
        self.status_code = status_code
        
def send_message_to_user(message, chatroom_id):
    try:
        chatroom_ref = firebase_db.collection("chatrooms").document(str(chatroom_id))

        chatroom_snapshot = chatroom_ref.get()
        if chatroom_snapshot.exists:
            chatroom_data = chatroom_snapshot.to_dict()

            # Use UTC time
            current_time_utc = datetime.now(timezone.utc)

            chatroom_data["lastMessageTimestamp"] = current_time_utc
            chatroom_data["lastMessageSenderId"] = current_user_id
            chatroom_data["lastMessage"] = message

            chatroom_ref.set(chatroom_data)

            chat_message_data = {
                "message": message,
                "senderId": current_user_id,
                "timestamp": current_time_utc,
                "audioUrl": ""
            }
            chatroom_ref.collection("chats").add(chat_message_data)

            return Response(status_code=200)
    except Exception as error:
        logger.error(error)


def send_audio_to_user(file_name, chatroom_id, response: str = None):
    try:
        chatroom_ref = firebase_db.collection("chatrooms").document(str(chatroom_id))

        chatroom_snapshot = chatroom_ref.get()
        if chatroom_snapshot.exists:
            chatroom_data = chatroom_snapshot.to_dict()

            # Use UTC time
            current_time_utc = datetime.now(timezone.utc)

            chatroom_data["lastMessageTimestamp"] = current_time_utc
            chatroom_data["lastMessageSenderId"] = current_user_id
            chatroom_data["lastMessage"] = "Audio"

            chatroom_ref.set(chatroom_data)

            chat_message_data = {
                "message": "Audio",
                "senderId": current_user_id,
                "timestamp": current_time_utc,
                "fileName": file_name,
                "text": response
            }
            chatroom_ref.collection("chats").add(chat_message_data)

            return Response(status_code=200)
    except Exception as error:
        logger.error(error)


def upload_audio_file(local_file_path, destination_file_path):
    try:
        # Get a reference to the storage bucket
        bucket = storage.bucket()
        # Create a reference to the file to upload
        blob = bucket.blob(destination_file_path)

        # Upload the file
        blob.upload_from_filename(local_file_path)

        # Get the download URL
        download_url = blob.generate_signed_url(expiration=3600)  # URL expires in 1 hour, adjust as needed

        print(f"File uploaded successfully to: {download_url}")
        return download_url
    except Exception as error:
        logger.error(error)