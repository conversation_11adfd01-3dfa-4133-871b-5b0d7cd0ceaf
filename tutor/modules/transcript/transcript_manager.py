import asyncio
import json


class TranscriptManager:
    def __init__(self, user):
        self.user = user
        self.transcripts = []

    def get_transcript(self) -> str:
        """Get the complete transcript of the AI call."""
        return " ".join(self.transcripts)

    def handle_partial_transcript(self, user_input: str):
        """Handle and send partial transcripts from the AI transcription service."""
        self._sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))

    def _sync_send(self, message: str):
        """Send a message to the user synchronously."""
        asyncio.run_coroutine_threadsafe(self.user.send(message), self.user.loop)
