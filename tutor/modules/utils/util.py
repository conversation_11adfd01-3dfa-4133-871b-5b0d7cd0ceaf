from datetime import datetime


def get_part_of_day() -> str:
    """Determine the current part of the day based on the current hour.

    Returns:
        str: One of "Morning", "Afternoon", "Evening", or "Night" based on the current time.
    """
    current_hour = datetime.now().hour
    if 5 <= current_hour < 12:
        return "Morning"
    elif 12 <= current_hour < 16:
        return "Afternoon"
    elif 16 <= current_hour < 20:
        return "Evening"
    else:
        return "Night"


def greeting_message() -> str:
    """Generate a greeting message based on the part of the day.

    Returns:
        str: A greeting message or an empty string if it is night time.
    """
    part_of_day = get_part_of_day()
    if part_of_day == "Night":
        return ""
    return f"Good {part_of_day}"


# Example usage
if __name__ == "__main__":
    print(greeting_message())
