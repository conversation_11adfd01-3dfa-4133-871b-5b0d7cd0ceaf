import re
import numpy as np
from fuzzywuzzy import fuzz


def extract_amounts(string):
    # Find all sequences of digits with optional decimal points in the string
    amounts = re.findall(r'\d+\.\d+|\d+', string)
    # Convert the sequences from strings to floats or integers as appropriate
    amounts = [float(amount) if '.' in amount else int(amount) for amount in amounts]
    return amounts


def partial_match_vehicle_number(transcript, vehicle_number_list):
    # Find vehicle numbers that contain the partial transcript
    matches = [vn for vn in vehicle_number_list if transcript in vn]
    return matches


def extract_preset_amount(transcript):
    # Example regex pattern for preset amount
    match = re.search(r'\b(\d+)\b', transcript)
    return int(match.group(0)) if match else None


def is_amount_in_vehicle_list(amount, vehicle_number_list, threshold=80):
    amount_str = str(amount)
    for vn in vehicle_number_list:
        if fuzz.partial_ratio(amount_str, vn) >= threshold:
            return True
    return False


def filter_amounts_not_in_vehicle_list(amounts, vehicle_number_list, threshold=80):
    unmatched_amounts = [amount for amount in amounts if
                         not is_amount_in_vehicle_list(amount, vehicle_number_list, threshold)]
    return unmatched_amounts


def select_correct_amount(sentence, unmatched_amounts):
    if not unmatched_amounts:
        return None

    if isinstance(sentence, int):
        sentence = str(sentence)  # Convert integer to string

    keywords = [
        "amount", "dollars", "rs", "rupees", "rupees", "preset", "charge", "balance", "set", "reset",
        "value", "cost", "price", "sum", "total", "fee", "expense", "payment",
        "deposit", "fund", "currency", "cash", "capital", "worth", "credit",
        "rate", "tariff", "figure", "quota", "valuation", "assessment",
        "estimation", "count", "number", "quantity", "figure", "update",
        "adjust", "revise", "amend", "modify", "alter", "change"
    ]

    keyword_positions = {keyword: sentence.lower().find(keyword) for keyword in keywords}

    # Sort unmatched amounts by relevance
    def relevance(amount):
        amount_str = str(amount)
        position_score = -sentence.lower().rfind(amount_str)  # prioritize later positions
        keyword_scores = [(sentence.lower().find(amount_str) - keyword_positions[keyword]) ** 2 for keyword in keywords
                          if keyword_positions[keyword] != -1]
        keyword_score = min(keyword_scores) if keyword_scores else float('inf')
        return position_score, keyword_score

    unmatched_amounts.sort(key=relevance)

    # Return the most relevant amount
    return unmatched_amounts[0]


def normalize_vehicle_number(normalized_text: str) -> str:
    if isinstance(normalized_text, int):
        normalized_text = str(normalized_text)  # Convert integer to string

    return normalized_text.replace(" ", "").lower()


def get_amount_from_string(sentence: str, vehicle_number_list):
    normalized_text_remove_space = normalize_vehicle_number(sentence)
    amounts = extract_amounts(normalized_text_remove_space)
    unmatched_amounts = filter_amounts_not_in_vehicle_list(amounts, vehicle_number_list)
    return select_correct_amount(sentence, unmatched_amounts)


def get_amount_from_user_response(sentence, minimum_amount, maximum_amount, vehicle_number_list):
    preset_amount = get_amount_from_string(sentence, vehicle_number_list)
    # if preset_amount is not None:
    if preset_amount:
        if maximum_amount >= preset_amount >= minimum_amount:
            # return f"{sentence}, my preset amount is {preset_amount}.", True
            return sentence.replace(str(preset_amount), f" {preset_amount}"), True
        elif preset_amount < minimum_amount:
            return f"Your amount is below the minimum limit of {minimum_amount}. Please provide an amount greater than or equal to {minimum_amount}.", False
        elif preset_amount >= maximum_amount:
            return f"Your amount exceeds the maximum limit of {maximum_amount}. Please provide an amount less than {maximum_amount}.", False
    else:
        return sentence, True


def get_amount_from_ai_response(output_preset_amount, minimum_amount, maximum_amount, vehicle_number_list):
    if output_preset_amount:
        preset_amount = get_amount_from_string(output_preset_amount, vehicle_number_list)
        if not preset_amount:
            try:
                preset_amount = extract_amounts(output_preset_amount)[0]
            except:
                preset_amount = None

        # if preset_amount is not None:
        if preset_amount:
            if maximum_amount >= preset_amount >= minimum_amount:
                return preset_amount, True
            elif preset_amount < minimum_amount:
                return f"Your amount is below the minimum limit of {minimum_amount}. Please provide an amount greater than or equal to {minimum_amount}.", False
            elif preset_amount >= maximum_amount:
                return f"Your amount exceeds the maximum limit of {maximum_amount}. Please provide an amount less than {maximum_amount}.", False
        else:
            return "you can provide preset amount", False
    else:
        return "you can provide preset amount", False

if __name__ == '__main__':
    print("test main")

"""
test_sentences = [
    "The total is rs 99,999.",
    "I need to set the preset amount to 50,000.",
    "Please charge me 1,500 rupees.",
    "The amount should be 2,50,000 for this transaction.",
    "I want to pay 75,000.50 dollars.",
    "Set the value at 1000000.",
    "The deposit is Rs. 10,00,000.",
    "Please update the balance to 5,00,000.",
    "The cost should be 3000000",
]

# Testing the sentences
from modules.text import word_to_number

results = []
for sentence in test_sentences:
    phrase = word_to_number.words_to_digits(sentence)
    result = get_amount_from_string(phrase, [])
    results.append((sentence, phrase, result))

# Print results
for sentence, phrase, result in results:
    print(f"Sentence: {sentence}")
    print(f"phrase: {phrase}")
    print(f"Extracted Amounts: {result}")
    print("=" * 40)


test_sentences = [
    "preset amount of 100 rupees",
    "rs 99",
    "amount 99",
    "preset 66",
    "23",
    "Idliketopresetmyvehicleabc123with5000 amount is 5000",
    "Pleasepresetvehiclexyz789with3000",
    "Vehicle abc123 should be updated with 4500 amount",
    "Presetmyvehicleab1234with5600 as new value",
    "Update the vehicle abc1234 amount to 1234",
    "Vehicle number xyz789 should be charged 7890 dollars",
    "The new amount for vehicle xyz789 should be 9876",
    "Please update the amount for vehicle xyz789 to 5000",
    "Charge vehicle abc123 with 3210 as the new amount",
    "Idliketopresetmyvehicleab123with2000 dollars",
    "Vehicle xyz789 has a new amount of 789 dollars",
    "Update vehicle abc123 amount to 6000 immediately",
    "Please set the vehicle number abc123 to 1000",
    "The charge for vehicle abc123 should be 3333",
    "Preset the vehicle xyz789 with the amount 4444",
    "Idliketopresetmyvehicleabc123with1234 immediately",
    "The new balance for vehicle abc123 is 2000 dollars",
    "Update my vehicle xyz789 to have 5555 as the amount",
    "The amount for vehicle abc2 should now be 6666",
    "Idliketopresetmyvehiclexyz789with4321 amount is 421"
]

vehicle_number_list = ["abc123", "xyz789"]

# Testing the sentences
results = []
for sentence in test_sentences:
    result = get_amount_from_string(sentence, vehicle_number_list)
    results.append(result)

print(results)

"""
