from word2number import w2n
import re


def is_number_word(word):
    try:
        w2n.word_to_num(word)
        return True
    except ValueError:
        return False


def extract_number_words(sentence):
    words = sentence.split()
    number_segments = []
    current_segment = []

    for word in words:
        if is_number_word(word):
            current_segment.append(word)
        else:
            if current_segment:
                number_segments.append(' '.join(current_segment))
                current_segment = []
            number_segments.append(word)

    if current_segment:
        number_segments.append(' '.join(current_segment))

    return number_segments


def convert_number_segments(segments):
    converted_segments = []
    for segment in segments:
        try:
            converted_segments.append(str(w2n.word_to_num(segment)))
        except ValueError:
            converted_segments.append(segment)
    return converted_segments


def reconstruct_sentence(segments):
    return ' '.join(segments)


def convert_numerical_words_to_digits(sentence):
    number_segments = extract_number_words(sentence)
    converted_segments = convert_number_segments(number_segments)
    return reconstruct_sentence(converted_segments)


def words_to_digits(text):
    text = remove_symbols_except_period(text)
    splits = text.split()
    text = ' '.join(word.replace('.', ' .') if '.' in word else word for word in splits)

    # text = remove_symbols(text)
    # Dictionary mapping words to digits
    word_to_digit = {
        "zero": "0", "one": "1", "two": "2", "three": "3", "four": "4",
        "five": "5", "six": "6", "seven": "7", "eight": "8", "nine": "9",
        "ten": "10", "eleven": "11", "twelve": "12", "thirteen": "13", "fourteen": "14",
        "fifteen": "15", "sixteen": "16", "seventeen": "17", "eighteen": "18", "nineteen": "19"
    }

    # Comprehensive set of words to skip conversion
    skip_words = {
        'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety',
        'hundred', 'thousand', 'million', 'billion'
    }

    # Check if any skip words are in the text
    if any(skip_word in text for skip_word in skip_words):
        return convert_numerical_words_to_digits(text)

    # Split the text into words
    words = text.split()

    # Replace words with digits
    result = [word_to_digit[word] if word in word_to_digit else word for word in words]

    # Join the words back into a string
    return ' '.join(result)


def remove_symbols_except_period(sentence):
    # Define a regular expression pattern that matches any character that is not a letter, a number, or a period
    pattern = re.compile('[^A-Za-z0-9. ]+')
    # Use the pattern to substitute all matching characters with an empty string
    cleaned_sentence = pattern.sub(' ', sentence)
    return cleaned_sentence


def remove_symbols(text):
    # Use regular expression to remove all non-alphanumeric characters
    cleaned_text = re.sub(r'[^a-zA-Z0-9]', ' ', text)
    return cleaned_text


"""
test_cases = [
    "Can you press it? Vehicle 1234.With preset amount of hundred rupees or chakra.",
    "preset amount of hundred rupees",
    "My vehicle number is 56 dhd two thousand three hundred forty five.",
    "I have one hundred and twenty five apples.",
    "She earned five thousand six hundred seventy eight dollars last month.",
    "The distance is three hundred miles.",
    "There are forty two students in the class.",
    "dh dh one two three four",
    "My vehicle number is fifty six dhd two thousand three hundred forty five.",
    "I have one hundred and twenty-five apples.",
    "She earned five thousand six hundred seventy eight dollars last month.",
    "The distance is three hundred miles.",
    "There are forty two students in the class.",
    "dh dh ten twenty one",
    "eleven",
    "twenty"
]

for sentence in test_cases:
    print(words_to_digits(sentence))
"""
