def format_vehicles_in_sentence(sentence, vehicles):
    # Ensure sentence and vehicles are not None
    if sentence is None or vehicles is None:
        return sentence

    for vehicle in vehicles:
        if vehicle in sentence:
            formatted_vehicle = ' '.join(vehicle.upper())
            sentence = sentence.replace(vehicle, formatted_vehicle)
    return sentence


"""
vehicles = ["abc123", "xyz2323"]
sentence = "My vehicle number is abc123."

output = format_vehicle_number(sentence, vehicles)
print(output)
"""
