from datetime import datetime
import importlib
import logging
import os
import random
from typing import Dict, List, Tuple, Union
import uuid

import pyodbc
from pydantic import FilePath


class Database:
    """Handles connection to an MS SQL database."""

    def __init__(self, database: str, server: str, user: str, password: str, timeout: int = 10, driver: str = "ODBC Driver 17 for SQL Server"):
        """Initialize Database connection.

        Args:
            database: Name of the MS SQL database.
            server: Server name or IP address.
            user: Username for authentication.
            password: Password for authentication.
            timeout: Connection timeout duration.
            driver: ODBC driver for SQL Server.
        """
        self.datastore = database
        self.connection = pyodbc.connect(
            f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};UID={user};PWD={password};Connection Timeout={timeout}"
        )
        # Create the Complaints table if it doesn't exist
        self.create_complaints_table()
        self.add_column("Complaints", "customer_name", "VARCHAR(50)")
        self.add_column("Complaints", "is_sentiment_analyzed", "BIT", default_value=0)
        self.add_column("Complaints", "is_posted", "BIT", default_value=0)
        self.add_column("Complaints", "sentiment_category", "VARCHAR(255)")
        self.add_column("Complaints", "sentiment_score", "INT", default_value=0)
        self.add_column("Complaints", "key_emotions", "VARCHAR(255)")

    def get_unanalyzed_records(self, table_name: str) -> List[Dict]:
        """Fetches records where sentiment analysis has not been completed.

        Args:
            table_name: The name of the table to query.

        Returns:
            A list of records (as dictionaries) where is_sentiment_analyzed is False.
        """
        query = f"SELECT * FROM [{table_name}] WHERE is_sentiment_analyzed = 0"
        
        with self.connection as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query)
                columns = [column[0] for column in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]
                return results
            except pyodbc.Error as e:
                logging.error(f"Error fetching unanalyzed records from {table_name}: {e}")
                raise
            finally:
                cursor.close()

    def update_record_with_sentiment(self, complaint_id: str, sentiment: str, score: int, emotions: List[str]) -> None:
        """Updates the record in the database with the analyzed sentiment category, score, and key emotions."""
        emotions_str = ", ".join(emotions)
        query = """
            UPDATE Complaints
            SET is_sentiment_analyzed = 1, sentiment_category = ?, sentiment_Score = ?, key_emotions = ?
            WHERE complaint_id = ?
        """
        with self.connection as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, (sentiment, score, emotions_str, complaint_id))
                conn.commit()
                logging.info(f"Updated complaint {complaint_id} with sentiment: {sentiment}, score: {score}, emotions: {emotions_str}")
            except Exception as e:
                logging.error(f"Error updating sentiment for complaint {complaint_id}: {e}")
                raise
            finally:
                cursor.close()

    def column_exists(self, table_name: str, column_name: str) -> bool:
        """Checks if a column exists in the specified table."""
        query = """
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND COLUMN_NAME = ?
        """
        with self.connection as conn:
            cursor = conn.cursor()
            cursor.execute(query, (table_name, column_name))
            result = cursor.fetchone()
            return result[0] > 0
        
    def add_column(self, table_name: str, column_name: str, data_type: str, default_value=None) -> None:
        """Adds a new column to an existing table if it does not already exist.

        Args:
            table_name: The name of the table to modify.
            column_name: The name of the new column to add.
            data_type: The data type of the new column (e.g., "BIT", "VARCHAR(255)").
            default_value: Optional default value for the new column.
        """
        # Check if column already exists
        if self.column_exists(table_name, column_name):
            logging.info(f"Column '{column_name}' already exists in table '{table_name}'.")
            return

        # Construct the SQL query to add a new column
        default_clause = f"DEFAULT {default_value}" if default_value is not None else ""
        query = f"""
            ALTER TABLE [{table_name}]
            ADD [{column_name}] {data_type} {default_clause};
        """

        with self.connection as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query)
                conn.commit()
                logging.info(f"Added column '{column_name}' with type '{data_type}' to table '{table_name}'.")
            except pyodbc.Error as e:
                logging.error(f"Error adding column {column_name} to table {table_name}: {e}")
                raise
            finally:
                cursor.close()

    def create_table(self, table_name: str, columns: Union[List[str], Tuple[str]]) -> None:
        """Creates a table with specified columns if it does not already exist.

        Args:
            table_name: Name of the table to create.
            columns: List of column definitions (e.g., "column_name BIT NOT NULL").
        """
        columns_formatted = ", ".join([f"[{col.split()[0]}] {col.split()[1]}" for col in columns])
        query = f"""
            IF OBJECT_ID('{table_name}', 'U') IS NULL
            BEGIN
                CREATE TABLE [{table_name}] ({columns_formatted})
            END
        """

        with self.connection as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query)
                conn.commit()
                logging.info(f"Table {table_name} created or already exists.")
            except pyodbc.Error as e:
                logging.error(f"Error creating table {table_name}: {e}")
                raise
            finally:
                cursor.close()

    def create_complaints_table(self) -> None:
        """Creates the Complaints table with the required schema."""
        table_name = "Complaints"
        columns = [
            "complaint_id VARCHAR(36) PRIMARY KEY",
            "account_id VARCHAR(50)",
            "subject VARCHAR(255)",
            "description TEXT",
            "status VARCHAR(50)",
            "date DATETIME"
        ]
        self.create_table(table_name, columns)

    def insert_complaint(self, account_id: str, customer_name: str, subject: str, description: str) -> str:
        """Inserts a complaint into the Complaints table and returns the complaint ID."""
        complaint_id = self.generate_complaint_id()
        query = """
            INSERT INTO Complaints (complaint_id, account_id, customer_name, subject, description, status, date)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        with self.connection as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, (
                    complaint_id,
                    account_id,
                    customer_name,
                    subject,
                    description,
                    "Pending",
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))
                conn.commit()
                logging.info(f"Complaint with ID {complaint_id} inserted successfully.")
                return complaint_id
            except pyodbc.Error as e:
                logging.error(f"Error inserting complaint: {e}")
                raise
            finally:
                cursor.close()

    def generate_complaint_id():
        from tutor.modules.utils import shared
        shared.seq_number += 1
        timestamp = datetime.now().strftime("%d%H%M%S%f")[:9]  # Format: ddHHmmsss
        return f"{timestamp}{shared.seq_number:03}"
        
    def create_table_1(self, table_name: str, columns: Union[List[str], Tuple[str]]) -> None:
        """Creates a table with specified columns if it does not already exist.

        Args:
            table_name: Name of the table to create.
            columns: List of columns with data types (e.g., "column_name BIT").
        """
        columns_formatted = ", ".join([f"[{col.split()[0]}] {col.split()[1]}" for col in columns])
        query = f"IF OBJECT_ID(?, 'U') IS NULL CREATE TABLE [{table_name}] ({columns_formatted})"
        
        with self.connection as conn:
            cursor = conn.cursor()
            try:
                cursor.execute(query, table_name)
                conn.commit()
                logging.info(f"Table {table_name} created successfully.")
            except pyodbc.Error as e:
                logging.error(f"Error creating table {table_name}: {e}")
                raise
            finally:
                cursor.close()


class __TestDatabase:
    """Provides test methods for database operations."""

    def __init__(self):
        """Sets up logging and database connection for tests."""
        importlib.reload(logging)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - [%(module)s:%(lineno)d] - %(funcName)s - %(message)s',
            datefmt='%b-%d-%Y %I:%M:%S %p'
        ))
        logging.root.addHandler(handler)
        logging.root.setLevel(logging.DEBUG)
        
        # Update connection details as needed
        self.db = Database(database="sampledb", server="192.168.1.5,1433", user="mayur", password="123456")

    def random_single(self) -> None:
        """Example using a single column."""
        self.db.create_table(table_name="TestDatabase", columns=["column BIT"])
        with self.db.connection as conn:
            cursor = conn.cursor()
            try:
                # Wrap "column" in square brackets to avoid keyword conflicts
                cursor.execute("INSERT INTO TestDatabase ([column]) VALUES (?);", (True,))
                conn.commit()
                if foo := cursor.execute("SELECT [column] FROM TestDatabase").fetchone():
                    logging.info(f"Fetched value from column: {foo[0]}")
                    cursor.execute("DELETE FROM TestDatabase WHERE [column]=1")
                    conn.commit()
                cursor.execute("DROP TABLE IF EXISTS TestDatabase")
                conn.commit()
                logging.info("Dropped table after testing.")
            except pyodbc.Error as e:
                logging.error(f"Error in random_single test: {e}")
                raise
            finally:
                cursor.close()


    def random_double(self) -> None:
        """Example using two columns with only one holding a value at any given time."""
        table_name = "TestDatabase"
        self.db.create_table(table_name=table_name, columns=["row BIT", "column BIT"])
        with self.db.connection as conn:
            cursor = conn.cursor()
            try:
                chosen_col = random.choice(['row', 'column'])
                # Wrap "column" and "row" in square brackets to avoid keyword conflicts
                cursor.execute(f"INSERT INTO TestDatabase ([{chosen_col}]) VALUES (?);", (True,))
                conn.commit()
                if (row := cursor.execute("SELECT [row] FROM TestDatabase").fetchone()) and row[0]:
                    logging.info(f"Row: {row[0]}")
                    cursor.execute("DELETE FROM TestDatabase WHERE [row]=1")
                    conn.commit()
                if (col := cursor.execute("SELECT [column] FROM TestDatabase").fetchone()) and col[0]:
                    logging.info(f"Column: {col[0]}")
                    cursor.execute("DELETE FROM TestDatabase WHERE [column]=1")
                    conn.commit()
                cursor.execute("DROP TABLE IF EXISTS TestDatabase")
                conn.commit()
                logging.info("Dropped table after testing.")
            except pyodbc.Error as e:
                logging.error(f"Error in random_double test: {e}")
                raise
            finally:
                cursor.close()



if __name__ == '__main__':
    test_db = __TestDatabase()
    test_db.random_single()
    test_db.random_double()
