import os
import re
import time
from threading import Thread

from typing import List, Dict, Any

from openai import OpenAIError

from executors import static_responses, files
from modules.exceptions import MissingEnvVars
from modules.logger import logger
import openai


class OngoChatbot:
    def __init__(self, mobile: str, session: str, logger):
        self.conversation_history = []
        self.model = "gpt-3.5-turbo"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}_ongo_chat_bot.yaml"
        self.faq_context = ""
        self.greeting = ""
        self.title = ""
        self.session = session
        self.mobile = mobile
        self.logger = logger

    def start_new_call(self):
        self._authenticate()
        if not self.authenticated:
            raise MissingEnvVars  # todo
        return self.greeting

    def answer_call_back(self, human_input: str = None, context: str = None) -> str:
        """Handles user input and returns the assistant's response.

        Args:
            human_input: The input text from the user.

        Returns:
            The response text from the assistant.
        """
        if not human_input:
            return "Sorry, I didn't get that. Please try again."

        return self._query(human_input.lower(), context)

    def _query(self, phrase: str, context: str) -> str:
        """Queries ChatGPT API with the request and returns the response.

        Args:
            phrase: The phrase spoken by the user.
        """
        self._update_context_message(context)

        phrase = phrase.lower()
        self.conversation_history.append(self._create_message("user", phrase))

        try:
            start_time = time.time()  # Start timing
            chat_response = self._get_chat_response()
            end_time = time.time()  # End timing
            response_time = end_time - start_time  # Calculate the duration
        except OpenAIError as error:
            logger.error(error)
            return static_responses.un_processable(self.title)

        if chat_response.choices:
            reply = chat_response.choices[0].message.content.lower()
            self.conversation_history.append(self._create_message("assistant", reply))
            Thread(target=self._dump_history, args=(phrase, reply, f"{response_time:.2f}")).start()
            return reply
        else:
            logger.error(chat_response)
            return static_responses.un_processable(self.title)

    def _update_context_message(self, context):
        if context and self.conversation_history:
            system_message = self.conversation_history[0]
            if system_message.get('role') == 'system':
                content = system_message['content']
                # Split the content to isolate the static prompt part
                if "ongo_context:" in content:
                    prompt_parts = content.split("ongo_context:")
                    prompt = prompt_parts[0].strip()
                    answer_context = prompt_parts[1].strip() if len(prompt_parts) > 1 else ""
                else:
                    prompt = content.strip()
                    answer_context = ""

                if context not in answer_context:
                    # Construct the new content
                    new_content = f"{prompt}\nongo_context:\n{context} {answer_context}".strip()
                    # Update the existing context message
                    self.conversation_history[0]['content'] = new_content

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file.

        Args:
            request: The request from the user.
            response: The response from the assistant.
        """
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)

    def _authenticate(self) -> None:
        """Initiates authentication and prepares GPT responses ready to be audio fed."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            chat_response = self._get_chat_response()
            self._process_chat_response(chat_response)
            self.authenticated = True
        except OpenAIError as error:
            logger.error(error)
        except Exception as error:
            logger.critical(error)

    def _process_chat_response(self, chat_response) -> None:
        """Processes the chat response and updates the conversation history."""
        response_content = chat_response.choices[0].message.content
        self.greeting = response_content
        # self.conversation_history.append(self._create_message("system", response_content))

    def _generate_initial_content(self) -> str:
        return (
            """
            You are an Ongo customer care assistant. Your responses will be audio fed,
            so keep them concise within 1 to 2 sentences without any parenthesis.
            You need to:
            1. Ask questions related to the Ongo prepaid card.
            2. Assist users with their queries related to the ONGO prepaid card.
            3. If the user question is related to the given ongo_context, provide an appropriate and accurate answer, Do ask follow-up questions if necessary.
            
            ongo_context:
                    
            """
        )

    def _get_chat_response(self):
        """Gets the chat response from the OpenAI API."""

        return openai.chat.completions.create(
            messages=self.conversation_history,
            model=self.model,
            temperature=0.3,
            max_tokens=50,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0
        )

    def _create_message(self, role: str, content: str) -> Dict[str, str]:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}
