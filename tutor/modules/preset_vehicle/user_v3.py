import asyncio
import json
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from typing import Optional

from api.ongo import ongo_app_api
from modules.audio.listener import AudioRecognizer
from modules.audio.text_speech import TextToSpeech
from modules.preset_vehicle import vehicle_preset_bot, ags_api_chain
from modules.preset_vehicle.bot import VehiclePresetAssistant
from modules.text import amount


class User:
    def __init__(self, session, name, websocket, data, event_loop):
        self.session = session
        self.name = name
        self.conn = websocket
        self.audio_queue = deque()
        self.transcripts = []
        self.voice_action_up = False
        self.pause = 1
        self.vehicle_number_str = None
        self.mobile = data.get('mobile')
        self.user_id = data.get('userId')
        self.vehicles = data.get('vehicles', [])
        self.vehicle_number_list = [vehicle['number'].lower() for vehicle in self.vehicles]
        self.assistant = VehiclePresetAssistant(self.vehicle_number_list)
        self.loop = event_loop  # asyncio.get_event_loop()
        self.recognizer = AudioRecognizer(self)
        self.is_end_call = False
        self.minimum_amount = 30
        self.maximum_amount = 100000
        self.ai_start_listening = False
        self.tts = TextToSpeech()
        self.audio_chunk_is_send = False
        self.executor = ThreadPoolExecutor()

    def __repr__(self):
        return (f"User(session={self.session}, name={self.name}, mobile={self.mobile}, "
                f"user_id={self.user_id}, vehicles={self.vehicles})")

    async def send(self, message):
        await self.conn.send(message)

    def sync_send(self, message):
        asyncio.run_coroutine_threadsafe(self.conn.send(message), self.loop)

    def start_ai_call(self):
        self.recognizer.start_ai_call()

    def end_ai_call(self):
        self.is_end_call = True
        self._run_async(self.conn.close())
        self.recognizer.save_audio_file()

    def _run_async(self, coroutine):
        asyncio.run_coroutine_threadsafe(coroutine, self.loop)

    def send_end_call(self):
        self.is_end_call = True
        self.sync_send(json.dumps({"type": "ai_end_call", "data": "ai call completed"}))
        self._run_async(self.conn.close())
        self.recognizer.save_audio_file()

    def get_transcript(self):
        return " ".join(self.transcripts)

    def handle_partial_transcript_from_transcription(self, user_input):
        self.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))

    def get_amount(self, user_input):
        return amount.get_amount_from_user_response(user_input, self.minimum_amount, self.maximum_amount,
                                                    self.vehicle_number_list)

    def is_vehicle_number_and_preset_amount_complete(self, llm_answer) -> tuple[bool, dict | None, bool, str | None]:
        """
        Check if the output is completed and convert the string to a JSON object.

        Parameters:
        llm_answer (str): The input string from the LLM.

        Returns:
        tuple: A tuple with a boolean indicating if completed and the JSON object or None.
        """
        phrase = "output: completed"
        is_phrase = False
        if phrase in llm_answer:
            is_phrase = True
            try:
                llm_answer_change = f"mobile_number:{self.mobile}, user_id:{self.user_id}, {llm_answer}"
                result = self.assistant.convert_to_json_combine(llm_answer_change)
                preset_amount = result.get('preset_amount')
                if result.get('vehicle_number') and preset_amount is not None:
                    preset_amount_check, is_preset_valid = amount.get_amount_from_ai_response(preset_amount,
                                                                                              self.minimum_amount,
                                                                                              self.maximum_amount,
                                                                                              self.vehicle_number_list)
                    if not is_preset_valid:
                        return False, None, is_phrase, preset_amount_check
                    result['preset_amount'] = preset_amount_check
                    result['vehicle_id'] = self.get_vehicle_id_by_number(result.get('vehicle_number'))
                    return True, result, is_phrase, None
            except ValueError as e:
                return True, None, is_phrase, None
        return False, None, is_phrase, None

    def get_vehicle_id_by_number(self, vehicle_number: str) -> Optional[str]:
        for vehicle in self.vehicles:
            if vehicle['number'].lower() == vehicle_number:
                return vehicle['id']
        return None

    def post_vehicle_number_and_preset_amount(self, ai_response, result, is_phrase):
        if result:
            is_valid_vehicle, response = self.validation_of_vehicle(result)
            if is_valid_vehicle:
                return ongo_app_api.get_response(result)
            else:
                return response
        else:
            if "vehicle" in ai_response and "preset" in ai_response:
                ai_response = ai_response
            else:
                ai_response = vehicle_preset_bot.get_current_vehicle_preset(self.assistant.conversation_history)

        # ai_response = bot.get_user_conversation_summary()
        # If any of the keywords are in the user_message, use api_chain
        return ags_api_chain.run(ai_response)

    def validation_of_vehicle(self, result):
        if result.get('vehicle_number') in self.vehicle_number_list:
            return True, ""
        else:
            return False, "error: please provide valid vehicle number"
