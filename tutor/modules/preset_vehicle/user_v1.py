from collections import deque


class User:
    def __init__(self, session, name, websocket):
        self.session = session
        self.name = name
        self.conn = websocket
        self.audio_queue = deque()
        self.transcripts = []
        self.voice_action_up = False
        self.pause = 5
        self.vehicle_number_str = None

    async def send(self, message):
        await self.conn.send(message)
