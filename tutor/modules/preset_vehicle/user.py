import asyncio
import json
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from threading import <PERSON>
from typing import List

import websockets
from modules.audio.audio_processor import AudioProcessor
from modules.logger import setup_logger
from modules.manager.bot_manager import BotManager
from modules.preset_vehicle.vehicle_validator import VehicleValidator

DISCONNECT_DELAY = 5  # delay in seconds

def process_data(data):
    vehicles = data.get('vehicles', [])
    vehicle_data = {}
    for vehicle in vehicles:
        make = vehicle['make'].lower()
        number = vehicle['number'].lower()
        if make in vehicle_data:
            vehicle_data[make].append(number)
        else:
            vehicle_data[make] = [number]
    return vehicle_data


class User:
    def __init__(self, session: str, name: str, websocket, data: dict, event_loop, active_users: List['User']):
        self.session = session
        self.name = name
        self.conn = websocket
        self.mobile = data.get('mobile')
        self.user_id = data.get('userId')
        self.vehicles = data.get('vehicles', [])
        self.vehicle_data = process_data(data)
        self.loop = event_loop
        self.is_end_call = False
        self.ai_start_listening = False
        self.executor = ThreadPoolExecutor()
        self.active_users = active_users

        self.logger = setup_logger(session, self.mobile)
        self.audio_processor = AudioProcessor(self, self.logger)
        self.realtime_transcription = None
        self.bot_manager = BotManager(self, self.logger)

        self._disconnect_task = None


    def __repr__(self):
        return (f"User(session={self.session}, name={self.name}, mobile={self.mobile}, "
                f"user_id={self.user_id}, vehicles={self.vehicles})")

    def to_dict(self):
            return {
                'session': self.session,
                'name': self.name,
                'mobile': self.mobile,
                'user_id': self.user_id,
                'vehicles': self.vehicles
            }
    
    async def send(self, message: str):
        """Send a message to the user."""
        try:
            if self.conn.open:
                await self.conn.send(message)
                # self.logger.info(f"Sent message to user: {message}")
            else:
                # self.logger.warning("Attempted to send a message on a closed connection")
                await self.schedule_disconnect()
        except websockets.ConnectionClosed:
            self.logger.error("Failed to send message: Connection closed", exc_info=True)   
            await self.schedule_disconnect()        
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}", exc_info=True)
            await self.schedule_disconnect()

    def sync_send(self, message: str):
        """Send a message to the user synchronously."""        
        asyncio.run_coroutine_threadsafe(self.send(message), self.loop)

    def start_ai_call(self):
        """Start AI call for the user."""
        self.logger.info("Starting AI call")
        self.audio_processor.start_ai_call()

    def end_ai_call(self):
        """End AI call for the user and save audio file."""
       
        if self.is_end_call:
            return
        self.logger.info("Ending AI call")
        self.is_end_call = True
        self._run_async(self.close())          
        self.audio_processor.save_audio_file()
        self._run_async(self.cleanup())
            

    def send_end_call(self):
        """Notify the user that the AI call has ended and close the connection."""
        if self.is_end_call:
            return
        self.logger.info("Sending end call notification to user")
        self.is_end_call = True
        self.sync_send(json.dumps({"type": "ai_end_call", "data": "ai call completed"}))
        self._run_async(self.close())
        self.audio_processor.save_audio_file()
        self._run_async(self.cleanup())

    async def close(self):
        try:
            await self.conn.close()
        except Exception as e:
            self.logger.error(f"Failed to close: {e}", exc_info=True)

    def _run_async(self, coroutine):
        """Run an asynchronous coroutine."""
        self.logger.info(f"Running asynchronous coroutine: {coroutine}")
        asyncio.run_coroutine_threadsafe(coroutine, self.loop)

    async def schedule_disconnect(self):
        """Schedule a task to disconnect the user after a delay."""
        if self._disconnect_task is None or self._disconnect_task.done():
            self.logger.info(f"Scheduling disconnect task for user {self.session}")
            self._disconnect_task = self.loop.create_task(self._disconnect_after_delay())

    async def _disconnect_after_delay(self):
        """Disconnect the user after a delay if the connection is still closed."""
        await asyncio.sleep(DISCONNECT_DELAY)
        if not self.conn.open:
            self.logger.info(f"Disconnecting user {self.session} after delay")
            self.audio_processor.save_audio_file()
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources after the user session ends."""
        if self in self.active_users:
            self.logger.info("Cleaning up user resources")
            self.executor.shutdown(wait=False)
            handlers = self.logger.handlers[:]
            for handler in handlers:
                handler.close()
                self.logger.removeHandler(handler)
            try:
                self.active_users.remove(self)
                self.logger.info(f"User {self.session} removed from active users")
            except ValueError:
                self.logger.error(f"User {self.session} could not be removed from active users")