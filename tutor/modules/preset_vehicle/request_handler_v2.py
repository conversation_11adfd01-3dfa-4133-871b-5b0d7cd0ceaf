import asyncio
import base64
import io
import json
import time

from executors import static_responses
from modules.audio import text_speech
from modules.text import word_to_number
from modules.text.number_formatter import format_numbers_with_spaces


class RequestHandler:
    def __init__(self, user, entry_dumper, logger):
        self.user = user
        self.entry_dumper = entry_dumper
        self.logger = logger
        self.is_completed = False
        self.last_llm_answer = ""

    def handle_final_transcript(self, user_input):
        while True:
            self.is_completed = False
            self.entry_dumper.start_dump_task("user", user_input)
            user_input = word_to_number.remove_symbols_except_period(user_input)
            self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))
            print("Pausing audio recording...")
            self.user.sync_send(json.dumps({"type": "pause_audio_recording", "data": ""}))
            print("User input:", user_input)

            is_amount = False
            llm_answer = None
            end_call = False

            start_time = time.time()
            # llm_answer = self.user.vehicle_validator.assistant.answer_call_back(user_input)
            # llm_answer = self.user.bot_manager.answer_call_back(user_input)
            llm_answer = self.answer_call_back(user_input)
            end_time = time.time()
            response_time = end_time - start_time
            print("Response time of self.answer_call_back:", response_time)
            print("AI response:", llm_answer)
            self.entry_dumper.start_dump_task("llm", f"{llm_answer}, response_time {response_time:.2f}")

            status, result, llm_response = self.user.bot_manager.process_llm_answer(llm_answer)
            if status:
                static_response = static_responses.sending_user_request()
                self.text_to_audio_chunk_send(static_response)
                self.entry_dumper.start_dump_task("static_response", static_response)

                llm_answer = self.user.bot_manager.post_vehicle_number_and_preset_amount(llm_answer, result)
                self.entry_dumper.start_dump_task("api_response", llm_answer)
                if "error:" in llm_answer:
                    llm_answer = llm_answer.replace("error:", "")
                else:
                    end_call = True
                    llm_answer = static_responses.get_success_message(result['preset_amount'],
                                                                      result['vehicle_number'])
                    self.entry_dumper.start_dump_task("get_success_message", llm_answer)
                    time.sleep(1)
            elif llm_response:
                llm_answer = llm_response
            else:
                new_llm_answer, is_end_no_vehicle = self.user.bot_manager.handle_no_vehicle_case(llm_answer)
                if is_end_no_vehicle:
                    end_call = True
                    llm_answer = new_llm_answer
                    self.entry_dumper.start_dump_task("get_no_vehicle_message", llm_answer)

            print("AI after validation response:", llm_answer)
            llm_answer = self.user.bot_manager.format_vehicle_number(llm_answer)
            llm_answer = format_numbers_with_spaces(llm_answer)
            self.text_to_audio_chunk_send(llm_answer)
            self.last_llm_answer = llm_answer
            self.entry_dumper.start_dump_task("after_validation", llm_answer)
            if end_call:
                time.sleep(1)
                self.user.send_end_call()
            else:
                self.is_completed = True
            break

    def text_to_audio_chunk_send(self, text):
        if text:
            async def send_audio(text_chunks):
                self.user.ai_start_listening = False
                self.user.audio_chunk_is_send = False

                for chunk in text_chunks:
                    buffer = io.BytesIO()
                    audio_chunk_size = 512
                    start_time = time.time()  # Start timing

                    # Call generate_audio asynchronously
                    audio_stream, audio_stream_1 = await asyncio.to_thread(text_speech.tts_instance.generate_audio,
                                                                           chunk)

                    end_time = time.time()  # End timing
                    print(f"generate_audio Response time for chunk '{chunk}': {end_time - start_time:0.2f} seconds")

                    try:
                        self.user.audio_processor.full_audio_buffer.write(audio_stream_1.read())

                        while True:
                            audio_chunk = audio_stream.read(audio_chunk_size)
                            if not audio_chunk:
                                break
                            buffer.write(audio_chunk)
                            if buffer.tell() >= audio_chunk_size:
                                buffer.seek(0)
                                audio_chunk_base64 = base64.b64encode(buffer.read(audio_chunk_size)).decode('utf-8')
                                await self.sync_send_async(
                                    json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
                                buffer.seek(0)
                                buffer.truncate(0)
                        if buffer.tell() > 0:
                            buffer.seek(0)
                            audio_chunk_base64 = base64.b64encode(buffer.read()).decode('utf-8')
                            await self.sync_send_async(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
                    except Exception as e:
                        print(f"Error processing chunk '{chunk}': {e}")

                self.user.audio_chunk_is_send = True

            # Split the text into chunks (for simplicity, let's split by sentences or a fixed number of characters)
            # Ensure chunks are not too short to avoid unnecessary processing overhead
            text_chunks = [sentence.strip() for sentence in text.split('. ') if sentence.strip()]

            user_loop = self.user.loop
            future = asyncio.run_coroutine_threadsafe(send_audio(text_chunks), user_loop)
            future.result()

    def text_to_audio_chunk_send_1(self, text):
        if text:
            async def send_audio():
                self.user.ai_start_listening = False
                self.user.audio_chunk_is_send = False

                start_time = time.time()  # Start timing

                # audio_stream, audio_stream_1 = self.user.audio_processor.tts.generate_audio(text)
                # Call generate_audio asynchronously
                audio_stream, audio_stream_1 = await asyncio.to_thread(text_speech.tts_instance.generate_audio,
                                                                       text)

                end_time = time.time()  # End timing
                print(f"generate_audio Response time: {end_time - start_time:0.2f} seconds")

                buffer = io.BytesIO()
                audio_chunk_size = 512
                try:
                    self.user.audio_processor.full_audio_buffer.write(audio_stream_1.read())

                    while True:
                        audio_chunk = audio_stream.read(audio_chunk_size)
                        if not audio_chunk:
                            break
                        buffer.write(audio_chunk)
                        if buffer.tell() >= audio_chunk_size:
                            buffer.seek(0)
                            audio_chunk_base64 = base64.b64encode(buffer.read(audio_chunk_size)).decode('utf-8')
                            await self.sync_send_async(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
                            buffer.seek(0)
                            buffer.truncate(0)
                    if buffer.tell() > 0:
                        buffer.seek(0)
                        audio_chunk_base64 = base64.b64encode(buffer.read()).decode('utf-8')
                        await self.sync_send_async(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
                finally:
                    self.user.audio_chunk_is_send = True

            user_loop = self.user.loop
            future = asyncio.run_coroutine_threadsafe(send_audio(), user_loop)
            future.result()

    async def sync_send_async(self, message):
        await self.user.send(message)

    def handle_partial_transcript(self, user_input):
        self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))

    def answer_call_back(self, user_input) -> str:
        response = None
        if user_input:
            async def get_llm_answer():
                return await self.user.bot_manager.answer_call_back(user_input)

            user_loop = self.user.loop
            future = asyncio.run_coroutine_threadsafe(get_llm_answer(), user_loop)
            response = future.result()
        if not response:
            response = "Sorry, I didn't get that."
        return response
