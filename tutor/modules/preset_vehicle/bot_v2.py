import os
from dotenv import load_dotenv

from dataclasses import dataclass
from typing import Literal

from langchain.chains import <PERSON>vers<PERSON><PERSON><PERSON><PERSON>
from langchain.memory import ConversationBufferMemory
from langchain.prompts.prompt import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.llms import OpenAI

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

vehicle_numbers = ["AB1234", "BC1234", "AC1234"]


@dataclass
class Message:
    """Class for keeping track of interview history."""
    role: Literal["user", "assistant"]
    content: str


greeting = "Thank you for calling On<PERSON>. could you please provide the vehicle number and the amount you wish to preset now?"

conversation_history = []

conversation_memory = ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant")

# llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0)
llm = OpenAI(model='gpt-3.5-turbo-instruct', temperature=0)

PROMPT_TEMPLATE = """
    You are a vehicle preset assistant. Your task is to gather information from the user regarding vehicle numbers and their respective preset amounts.

    1. Ask questions to obtain both the vehicle number and preset amount from the user.
    2. Ensure that both the vehicle number and preset amount are not null or empty.
    3. Validate the vehicle number against the provided list of {vehicle_numbers}.
    4. Ensure the preset amount is within the range of 100 to 10,000.
    5. After obtaining the vehicle number and preset amount, confirm them with the user to ensure accuracy.
    
    Remember to:    
    * Keep your responses short (1 or 2 sentences).
    * Ask only one question at a time.
    * Prompt follow-up questions if necessary.
    * Do not write explanations.
    
    Once you have confirmed the vehicle number and preset amount with the user, respond with "Output: completed".
    
    Current Conversation:
    {history}
    User: {input}
    Assistant:
    """

PROMPT_TEMPLATE = PROMPT_TEMPLATE.replace("{vehicle_numbers}", ", ".join(vehicle_numbers))

PROMPT = PromptTemplate(input_variables=["history", "input"], template=PROMPT_TEMPLATE)

# Function to create a new conversation chain instance
def create_conversation_chain():
    return ConversationChain(prompt=PROMPT, llm=llm, memory=ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant"))

conversation_chain = create_conversation_chain()

def answer_call_back(human_input: str):
    if not human_input:
        input_text = "Sorry, I didn't get that. Please try again."
        # conversation_history.append(Message("ai", input_text))
        return input_text
    else:
        input_text = human_input

    conversation_history.append(Message("user", input_text))
    ai_response = conversation_chain.run(input_text)
    conversation_history.append(Message("assistant", ai_response))
    return ai_response


def start_new_call():
    global conversation_chain
    conversation_chain = create_conversation_chain()
    conversation_history.clear()

# Example usage

#print(greeting)
#print(answer_call_back("My vehicle number is AB1234 and I want to preset 500."))
#print(answer_call_back("yes"))
start_new_call()  # This will reset the conversation
print(answer_call_back("My vehicle number is AC1234 and I want to preset 1000."))
print(answer_call_back("no"))
print(answer_call_back("preset amount is 100"))


