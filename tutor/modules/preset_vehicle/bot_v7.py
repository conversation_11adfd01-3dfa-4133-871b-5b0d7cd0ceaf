import os
from dotenv import load_dotenv
import re
import json
from dataclasses import dataclass
from typing import Literal, Optional, Tuple, Any, List

from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from langchain.chains import Conversation<PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.prompts.prompt import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.llms import OpenAI

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')


@dataclass
class Message:
    """Class for keeping track of interview history."""
    role: Literal["user", "assistant"]
    content: str


class VehiclePresetAssistant:
    def __init__(self, vehicle_number_list: List[str]):
        self.vehicle_number_list = vehicle_number_list
        self.greeting = "Thank you for calling Ongo. Could you please provide the vehicle number you wish to preset now?"
        self.conversation_history = []
        self.llm = OpenAI(model='gpt-3.5-turbo-instruct', temperature=0)
        self.conversation_memory = ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant")
        self.PROMPT_TEMPLATE = """
            You are a vehicle preset assistant. Your task is to gather information from the user regarding vehicle numbers and their respective preset amounts.

            1. Ask questions to obtain both the vehicle number and preset amount from the user.
            2. Ensure that both the vehicle number and preset amount are not null or empty.
            3. Validate the vehicle number against the user list of vehicle numbers ({vehicle_numbers}). If the provided vehicle number is not in the list, prompt the user to provide a valid vehicle number.
            4. Ensure the preset amount is within the range of 30 to 100000.
            5. Once you have obtained the vehicle number is in the list of vehicle numbers ({vehicle_numbers}) and preset amount from the user, respond with "vehicle_number: 'vehicle number', preset_amount: 'preset amount', output: completed".
            
            If the user asks for related information about the vehicle list or vehicle numbers, provide the following:
            "Here is a list of your vehicle numbers: {vehicle_numbers}."

            Remember to:
            * All your response will be audio fed    
            * Keep your responses short (1 or 2 sentences).
            * Ask only one question at a time.
            * Prompt follow-up questions if necessary.
            * Avoid explanations.

            Current Conversation:
            {history}
            User: {input}
            Assistant:
            """
        self.PROMPT_TEMPLATE = self.PROMPT_TEMPLATE.replace("{vehicle_numbers}", ", ".join(self.vehicle_number_list))
        self.PROMPT = PromptTemplate(input_variables=["history", "input"], template=self.PROMPT_TEMPLATE)
        self.conversation_chain = self.create_conversation_chain()

    def create_conversation_chain(self):
        return ConversationChain(prompt=self.PROMPT, llm=self.llm, memory=self.conversation_memory)

    def answer_call_back(self, human_input: str = None):
        if not human_input:
            input_text = "Sorry, I didn't get that. Please try again."
            return input_text
        else:
            input_text = human_input

        self.conversation_history.append(Message("user", input_text))
        ai_response = self.conversation_chain.run(input_text).lower()
        ai_response = ai_response.replace("Assistant:", "")
        self.conversation_history.append(Message("assistant", ai_response))
        return ai_response.replace("100000", "1 lakh")

    def start_new_call(self):
        self.conversation_chain = self.create_conversation_chain()
        self.conversation_history.clear()

    def extract_vehicle_number_and_amount(self, text: str) -> Tuple[Optional[str], Optional[int], Optional[str]]:
        normalized_text = text.lower()
        normalized_text_remove_space = self.normalize_vehicle_number(normalized_text)

        vehicle_number = None
        preset_amount = None

        best_match, ratio = process.extractOne(normalized_text_remove_space, self.vehicle_number_list)
        sub_string = ""
        if ratio >= 40:
            vehicle_number = best_match
            match_start = -1
            match_end = -1
            for i in range(len(normalized_text_remove_space) - len(best_match) + 1):
                sub_string = normalized_text_remove_space[i:i + len(best_match)]
                ratio = process.extractOne(sub_string, [best_match])[1]
                if ratio >= 80:
                    match_start = i
                    match_end = i + len(best_match)
                    break

            if match_start != -1:
                normalized_text_remove_space = normalized_text_remove_space[
                                               :match_start] + normalized_text_remove_space[match_end:]

        amount_pattern = re.compile(r'\b(\d+)\b')
        amount_match = amount_pattern.search(normalized_text_remove_space)

        if amount_match:
            preset_amount = int(amount_match.group(0))
            if not (100 <= preset_amount <= 10000):
                preset_amount = None

        return vehicle_number, preset_amount, sub_string

    def normalize_vehicle_number(self, vehicle_number: str) -> str:
        return vehicle_number.replace(" ", "").lower()

    def count_vehicle_numbers_with_substring(self, sub_string: str) -> int:
        return sum(1 for vehicle in self.vehicle_number_list if sub_string in vehicle)

    def get_user_conversation_summary(self) -> Optional[str]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        for message in self.conversation_history:
            if message.role == "user":
                potential_vehicle_number, potential_amount, sub_string = self.extract_vehicle_number_and_amount(
                    message.content)
                if potential_vehicle_number:
                    vehicle_number = potential_vehicle_number
                if potential_amount:
                    preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {self.count_vehicle_numbers_with_substring(sub_string)}"
        elif vehicle_number:
            return f", Vehicle number is {vehicle_number}."
        elif preset_amount:
            return ""
        else:
            return ""

    def get_user_vehicle_number(self, response: str) -> Tuple[str, bool]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        if response:
            potential_vehicle_number, potential_amount, sub_string = self.extract_vehicle_number_and_amount(response)
            if potential_vehicle_number:
                vehicle_number = potential_vehicle_number
            if potential_amount:
                preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"{response}, Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {self.count_vehicle_numbers_with_substring(sub_string)}", True
        elif vehicle_number:
            return f"Vehicle number is {vehicle_number}.", True
        elif preset_amount:
            return response, False
        else:
            return response, False

    def convert_to_json_combine(self, input_string: str) -> dict:
        def parse_value(value):
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value

        input_string = input_string.strip().lower()
        pattern = re.compile(r"\s*(\w+(?: \w+)*?)\s*:\s*'?(.*?)'?\s*(?=,|;|$)")
        matches = pattern.findall(input_string)

        data_dict = {}
        for key, value in matches:
            key = key.strip().replace(' ', '_')
            data_dict[key] = parse_value(value.strip())

        return data_dict

    def print_interaction(self, user_input: str, response: str):
        print(f"User: {user_input}")
        print(f"Assistant: {response}\n")


"""
# Example usage:
# vehicle_number_list = ["ABC123", "XYZ789"]
# assistant = VehiclePresetAssistant(vehicle_number_list)
# assistant.start_new_call()
# print(assistant.answer_call_back("I'd like to preset my vehicle ABC123 with 5000"))

# Test strings
test_strings = [
    "Vehicle number: ap31az8789, preset amount: 10000, output: completed.",
    "Vehicle number: ap 31 az 8789, preset amount: 20000, status: in progress, message: all good.",
    "Vehicle number: 'ap31az8789', preset amount: '10000', output: 'completed'.",
    "Vehicle number: ap31az8789, preset amount: 10000.50, discount: 25, output: completed, approved: True.",
    "Vehicle number: ap31az8789, preset amount: 10000.75, tax rate: 0.18, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, approved: True, output: completed.",
    "Vehicle number: ap31az8789, description: high-end model, preset amount: 10000, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, output: , approved: True.",
    "output: completed.",
    "Vehicle number:ap31az8789,preset amount:10000,output:completed.",
    "Vehicle number: ap31az8789; preset amount: 10000; output: completed.",
    "Vehicle details: {'number': 'ap31az8789', 'model': 'Sedan'}, preset amount: 10000, output: completed.",
    " Vehicle number :  ap31az8789 ,  preset amount :  10000 ,  output :  completed . ",
    "Vehicle number: ap31az8789, preset amount:, output: completed."
]

for i, test_string in enumerate(test_strings, 1):
    result_dict = convert_to_json_combine(test_string)
    print(f"Test Case {i}:\n{json.dumps(result_dict, indent=4)}\n")


conversation_history.append(Message("user", "My vehicle number is ABC 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is a b 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 1 2 3 and I want to preset 5000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 12 34 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 2 34 and I want to preset 50000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is 1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()


# Simulate the conversation with debugging outputs
print("Initial Greeting:")
print(greeting)
print()

user_input = "My vehicle number is AB1234 and I want to preset 500."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 500."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is XYZ1234 and I want to preset 500."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234 and I want to preset 50000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

start_new_call()
user_input = "My vehicle number is A123, Vehicle number: ab1234".lower()
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

# Start a new call to reset the conversation
print("Starting a new call:")
start_new_call()
print(greeting)
print()

user_input = "My vehicle number is AC1234 and I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AC1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000"
response = answer_call_back(user_input)
print_interaction(user_input, response)
"""
