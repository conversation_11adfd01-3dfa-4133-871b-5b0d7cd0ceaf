from langchain.prompts import PromptTemplate

api_url_template = """
Given the following API Documentation for ongo official vehicle preset store API: {api_docs}
Your task is to construct the most efficient API URL to answer the user's question, ensuring the 
call is optimized to include only necessary information.
Question: {question}
API URL:
"""
api_url_prompt = PromptTemplate(input_variables=['api_docs', 'question'],
                                template=api_url_template)

api_response_template = """
With the API Documentation for ongo's official API: {api_docs} and the specific user question: {question} in mind,
and given this API URL: {api_url} for querying, here is the response from ongo's API: {api_response}.
If the API response indicates an error, summarize the error message prefixed with 'error:'.
Otherwise, please provide a summary that directly addresses the user's question,
omitting technical details like response format, and focusing on delivering the answer with clarity and conciseness,
as if ongo itself is providing this information.
Summary:
"{api_response}"
"""

api_response_prompt = PromptTemplate(input_variables=['api_docs', 'question', 'api_url',
                                                      'api_response'],
                                     template=api_response_template)
