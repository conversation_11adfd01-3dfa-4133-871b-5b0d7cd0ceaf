import os
import re
from threading import Thread
from typing import Op<PERSON>, Tuple, List

import openai
from dotenv import load_dotenv
from fuzzywuzzy import process
from langchain.llms import OpenAI
from openai import OpenAIError

from executors import static_responses, files
from modules.exceptions import MissingEnvVars
from modules.logger import logger
from modules.models.classes import Message
from modules.text import word_to_number
from modules.utils import util

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')


class VehiclePresetAssistant:
    def __init__(self, session: str, full_name: str, vehicle_number_list: List[str]):
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.vehicle_number_list = vehicle_number_list
        # self.greeting = "Thank you for calling Ongo. Could you please provide the vehicle number you wish to preset now?"
        self.greeting = f"{static_responses.greeting()} {self.first_name}, Good {util.part_of_day()}!, Thank you for calling ongo, "
        self.conversation_history = []
        self.llm = OpenAI()
        self.authenticated = False
        self.model = "gpt-3.5-turbo"  # "gpt-3.5-turbo" "gpt-4o"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}.yaml"
        self.minimum_amount = 30
        self.maximum_amount = 100000
        self.title = ""  # sir or madam
        self.session = session

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_call(self):
        self.authenticate()
        if not self.authenticated:
            raise MissingEnvVars
        return self.greeting

    def authenticate(self) -> None:
        """Initiates authentication and prepares GPT responses ready to be audio fed."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            chat_response = self._get_chat_response()
            self._process_chat_response(chat_response)
            self.authenticated = True
        except OpenAIError as error:
            logger.error(error)
        except Exception as error:
            logger.critical(error)

    def query(self, phrase: str) -> str:
        """Queries ChatGPT API with the request and returns the response.

        Args:
            phrase: The phrase spoken by the user.
        """
        phrase = phrase.lower()
        self.conversation_history.append(self._create_message("user", phrase))

        try:
            chat_response = self._get_chat_response()
        except OpenAIError as error:
            logger.error(error)
            return static_responses.un_processable(self.title)

        if chat_response.choices:
            reply = chat_response.choices[0].message.content.lower()
            self.conversation_history.append(self._create_message("assistant", reply))
            Thread(target=self._dump_history, args=(phrase, reply)).start()
            return reply
        else:
            logger.error(chat_response)
            return static_responses.un_processable(self.title)

    def answer_call_back(self, human_input: str = None) -> str:
        """Handles user input and returns the assistant's response.

        Args:
            human_input: The input text from the user.

        Returns:
            The response text from the assistant.
        """
        if not human_input:
            return "Sorry, I didn't get that. Please try again."

        return self.query(human_input.lower())

    def _dump_history(self, request: str, response: str) -> None:
        """Dumps the conversation history to a YAML file.

        Args:
            request: The request from the user.
            response: The response from the assistant.
        """
        data = files.load_yaml_from_session(self.session, self.file_name) or []
        data.append({'request': request, 'response': response})
        files.save_yaml_to_session(self.session, self.file_name, data)

    def _generate_initial_content(self) -> str:
        """Generates the initial content based on the vehicle number list."""
        base_content = (
            "You are a vehicle preset amount assistant. Your responses will be audio fed, "
            "so keep them concise within 1 to 2 sentences without any parenthesis. "
            "You need to: "
        )

        if len(self.vehicle_number_list) == 1:
            vehicle_number = self.vehicle_number_list[0]
            content = (
                f"{base_content}"
                f"1. Directly ask for the preset amount for the vehicle number '{vehicle_number}'. "
                "2. Ensure that the preset amount is not null or empty. "
                "3. Once you have obtained the preset amount, convert it to an integer. "
                f"4. Ensure the vehicle number is '{vehicle_number}'. "
                f"5. Once you have obtained the vehicle number and preset amount, respond only "
                f"'vehicle_number: '{vehicle_number}', preset_amount: 'preset amount', output: completed'."
            )
        else:
            vehicle_numbers = ", ".join(self.vehicle_number_list).lower()
            content = (
                f"{base_content}"
                "1. Ask questions to obtain the vehicle number and preset amount from the user. "
                "2. Ensure that both the vehicle number and preset amount are not null or empty. "
                "3. Once you have obtained the preset amount, convert it to an integer. "
                f"4. If the user requests a list of vehicle numbers, provide the list and then ask a follow-up question related to 'Which vehicle number are you setting the preset amount for?'. "
                f"5. Ensure the vehicle number is in the list of user vehicle numbers ({vehicle_numbers}). "
                "6. Once you have obtained the vehicle number and preset amount, respond only "
                "'vehicle_number: 'vehicle number', preset_amount: 'preset amount', output: completed'."
            )

        return content

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _get_chat_response(self):
        """Gets the chat response from the OpenAI API."""

        return openai.chat.completions.create(
            messages=self.conversation_history,
            model=self.model,
            temperature=0.3,
            max_tokens=50,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0
        )

    def _process_chat_response(self, chat_response) -> None:
        """Processes the chat response and updates the conversation history."""
        response_content = chat_response.choices[0].message.content
        self.greeting += response_content
        self.conversation_history.append(self._create_message("system", response_content))

    def extract_vehicle_number_and_amount(self, text: str) -> Tuple[Optional[str], Optional[int], Optional[str]]:
        normalized_text = text.lower()
        normalized_text_remove_space = self.normalize_vehicle_number(normalized_text)

        vehicle_number = None
        preset_amount = None

        best_match, ratio = process.extractOne(normalized_text_remove_space, self.vehicle_number_list)
        sub_string = ""
        if ratio >= 40:
            vehicle_number = best_match
            match_start = -1
            match_end = -1
            for i in range(len(normalized_text_remove_space) - len(best_match) + 1):
                sub_string = normalized_text_remove_space[i:i + len(best_match)]
                ratio = process.extractOne(sub_string, [best_match])[1]
                if ratio >= 80:
                    match_start = i
                    match_end = i + len(best_match)
                    break

            if match_start != -1:
                normalized_text_remove_space = normalized_text_remove_space[
                                               :match_start] + normalized_text_remove_space[match_end:]

        return vehicle_number, preset_amount, sub_string

    def normalize_vehicle_number(self, vehicle_number: str) -> str:
        return vehicle_number.replace(" ", "").lower()

    def count_vehicle_numbers_with_substring(self, sub_string: str) -> int:
        return sum(1 for vehicle in self.vehicle_number_list if sub_string in vehicle)

    def _get_user_conversation_summary(self) -> Optional[str]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        for message in self.conversation_history:
            if message.role == "user":
                potential_vehicle_number, potential_amount, sub_string = self.extract_vehicle_number_and_amount(
                    message.content)
                if potential_vehicle_number:
                    vehicle_number = potential_vehicle_number
                if potential_amount:
                    preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {self.count_vehicle_numbers_with_substring(sub_string)}"
        elif vehicle_number:
            return f", Vehicle number is {vehicle_number}."
        elif preset_amount:
            return ""
        else:
            return ""

    def _remove_symbols(self, sentence):
        # Define a regular expression pattern that matches any character that is not a letter, a number, or a period
        pattern = re.compile('[^A-Za-z0-9 ]+')
        # Use the pattern to substitute all matching characters with an empty string
        cleaned_sentence = pattern.sub(' ', sentence)
        return cleaned_sentence

    def get_user_vehicle_number(self, response: str) -> Tuple[str, bool]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        if response:
            response = word_to_number.words_to_digits(response)
            response = self._remove_symbols(response)
            potential_vehicle_number, potential_amount, sub_string = self.extract_vehicle_number_and_amount(response)
            if potential_vehicle_number:
                vehicle_number = potential_vehicle_number
            if potential_amount:
                preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"my vehicle number is {vehicle_number}, preset amount is {preset_amount}.", True
        elif vehicle_number:
            return f"my vehicle number is {vehicle_number}.", True
        elif preset_amount:
            return response, False
        else:
            return response, False

    def convert_to_json_combine(self, input_string: str) -> dict:
        def parse_value(value):
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value

        input_string = input_string.strip().lower()
        pattern = re.compile(r"\s*(\w+(?: \w+)*?)\s*:\s*'?(.*?)'?\s*(?=,|;|$)")
        matches = pattern.findall(input_string)

        data_dict = {}
        for key, value in matches:
            key = key.strip().replace(' ', '_')
            data_dict[key] = parse_value(value.strip())

        return data_dict


def print_interaction(user_input: str, response: str):
    print(f"User: {user_input}")
    print(f"Assistant: {response}\n")


"""

print(remove_symbols("A B 123 and 100, Sorry, my vehicle @ $^&&^%!number is ABC.123."))

# Example usage:
vehicle_number_list = ["ABC123", "XYZ789"]
assistant = VehiclePresetAssistant(vehicle_number_list)
assistant.start_new_call()

print_interaction("What vehicles do you have?", assistant.answer_call_back("What vehicles do you have?"))
print_interaction("A B 123 and 100, Sorry, my vehicle number is ABC123.",
                  assistant.answer_call_back("A B 123 and 100, Sorry, my vehicle number is ABC123."))
user_input = "1000"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)
if "completed" in response:
    print("done")
else:
    user_input = "yes"
    response = assistant.answer_call_back(user_input)
    print_interaction(user_input, response)


test_strings = [
    "Can you give me a list of available vehicle numbers?",
    "What vehicles do you have?",
    "Please provide the vehicle numbers.",
    "List all the vehicle numbers.",
    "I need to see the available vehicles.",
    "Show me the vehicle list.",
    "Can I get a list of the vehicles?",
    "What are the vehicle numbers I can choose from?",
    "Provide me with the list of vehicles.",
    "Tell me the available vehicle numbers.",
    "Could you list the vehicle numbers for me?",
    "What vehicle numbers do you have?",
    "Give me the list of vehicle numbers.",
    "I want to know the vehicle numbers.",
    "Please show me the list of vehicles.",
]
for i, test_string in enumerate(test_strings, 1):
    print_interaction("", assistant.start_new_call())
    print_interaction(test_string, assistant.answer_call_back(test_string))
"""

"""
# print(assistant.answer_call_back("I'd like to preset my vehicle ABC123 and preset amount 123"))
print(assistant.answer_call_back("ABC123"))

print(assistant.answer_call_back("123"))

print(assistant.answer_call_back("5000"))

# Test strings
test_strings = [
    "Vehicle number: ap31az8789, preset amount: 10000, output: completed.",
    "Vehicle number: ap 31 az 8789, preset amount: 20000, status: in progress, message: all good.",
    "Vehicle number: 'ap31az8789', preset amount: '10000', output: 'completed'.",
    "Vehicle number: ap31az8789, preset amount: 10000.50, discount: 25, output: completed, approved: True.",
    "Vehicle number: ap31az8789, preset amount: 10000.75, tax rate: 0.18, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, approved: True, output: completed.",
    "Vehicle number: ap31az8789, description: high-end model, preset amount: 10000, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, output: , approved: True.",
    "output: completed.",
    "Vehicle number:ap31az8789,preset amount:10000,output:completed.",
    "Vehicle number: ap31az8789; preset amount: 10000; output: completed.",
    "Vehicle details: {'number': 'ap31az8789', 'model': 'Sedan'}, preset amount: 10000, output: completed.",
    " Vehicle number :  ap31az8789 ,  preset amount :  10000 ,  output :  completed . ",
    "Vehicle number: ap31az8789, preset amount:, output: completed."
]

for i, test_string in enumerate(test_strings, 1):
    result_dict = convert_to_json_combine(test_string)
    print(f"Test Case {i}:\n{json.dumps(result_dict, indent=4)}\n")


conversation_history.append(Message("user", "My vehicle number is ABC 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is a b 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 1 2 3 and I want to preset 5000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 12 34 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 2 34 and I want to preset 50000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is 1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()


# Simulate the conversation with debugging outputs
print("Initial Greeting:")
print(greeting)
print()

vehicle_number_list = ["AB1234", "XYZ789"]
assistant = VehiclePresetAssistant(vehicle_number_list)

user_input = "My vehicle number is AB1234 and I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "can you provide me vehicle list"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "ab1234 and 500"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is ab 1 2 and I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is A123, my vehicle number is ab1234"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 25."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234 and I want to preset 50000."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)
# Start a new call to reset the conversation
print("Starting a new call:")
start_new_call()
print(greeting)
print()

user_input = "My vehicle number is AC1234 and I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AC1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000"
response = answer_call_back(user_input)
print_interaction(user_input, response)
"""
