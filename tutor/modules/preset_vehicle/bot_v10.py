import os
import re
from typing import Op<PERSON>, Tuple, List

import openai
from dotenv import load_dotenv
from fuzzywuzzy import process
from langchain.llms import OpenAI
from openai import OpenAIError

from modules.exceptions import MissingEnvVars

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')


class Message:
    def __init__(self, role: str, content: str):
        self.role = role
        self.content = content

    def to_dict(self):
        return {"role": self.role, "content": self.content}


class VehiclePresetAssistant:
    def __init__(self, vehicle_number_list: List[str]):
        self.vehicle_number_list = vehicle_number_list
        # self.greeting = "Thank you for calling Ongo. Could you please provide the vehicle number you wish to preset now?"
        self.greeting = "Thank you for calling Ongo. "
        self.conversation_history = []
        self.llm = OpenAI()
        self.authenticated = False
        self.model = "gpt-3.5-turbo"  # "gpt-3.5-turbo" "gpt-4o"
        self.minimum_amount = 30
        self.maximum_amount = 100000

    def authenticate(self) -> None:
        """Initiates authentication and prepares GPT responses ready to be audio fed."""
        if os.getenv('OPENAI_API_KEY'):
            os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
            openai.api_key = os.getenv('OPENAI_API_KEY')
        else:
            # logger.warning("'openai_api' wasn't found to proceed")
            return
        content = (
            "You are a vehicle preset amount assistant. Your responses will be audio fed, "
            "so keep them concise within 1 to 2 sentences without any parenthesis. "
            "You need to: "
            "1. Ask questions to obtain the vehicle number and preset amount from the user. "
            "2. Ensure that both the vehicle number and preset amount are not null or empty. "
            "3. Once you have obtained the preset amount, convert it to an integer. "
            "4. If the user requests a list of vehicle numbers, provide the list and then ask a follow-up question related to ""Which vehicle number are you setting the preset amount for?"" "
            "5. Ensure the vehicle number is in the list of user vehicle numbers ({vehicle_numbers}). "
            "6. Once you have obtained the vehicle number and preset amount, respond only "
            "'vehicle_number: 'vehicle number', preset_amount: 'preset amount', output: completed'."
        )

        content = content.replace("{vehicle_numbers}", ", ".join(self.vehicle_number_list).lower())
        self.conversation_history.append(Message("system", content).to_dict())
        try:
            chat = openai.chat.completions.create(
                messages=self.conversation_history,
                model=self.model,
                temperature=0.3,
                max_tokens=50,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
            self.greeting = self.greeting + chat.choices[0].message.content
            self.conversation_history.append(Message("system", chat.choices[0].message.content).to_dict())
            self.authenticated = True
        except OpenAIError as error:
            # logger.error(error)
            pass
        except Exception as error:
            pass
        # logger.critical(error)

    def start_new_call(self):
        self.authenticate()
        if not self.authenticated:
            raise MissingEnvVars
        return self.greeting

    def sending_user_request(self):
        return "we are working on your request right now, please wait."

    def query(self, phrase: str) -> str:
        """Queries ChatGPT api with the request and speaks the response.

        See Also:
            - Even without authentication, this plugin can fetch responses from a mapping file.
            - This allows, reuse-ability for requests in identical pattern.

        Args:
            phrase: Takes the phrase spoken as an argument.
        """
        phrase = phrase.lower()
        self.conversation_history.append(Message("user", phrase).to_dict())

        try:
            chat = openai.chat.completions.create(
                messages=self.conversation_history,
                model=self.model,
                temperature=0.3,
                max_tokens=50,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
        except OpenAIError as error:
            # logger.error(error)
            return ""
        if chat.choices:
            reply = chat.choices[0].message.content.lower()
            self.conversation_history.append(Message("assistant", reply).to_dict())
            return reply
        else:
            return ""
            # logger.error(chat)

    def answer_call_back(self, human_input: str = None):
        if not human_input:
            input_text = "Sorry, I didn't get that. Please try again."
            return input_text
        else:
            input_text = human_input

        return self.query(input_text.lower()).lower()

    def extract_vehicle_number_and_amount(self, text: str) -> Tuple[Optional[str], Optional[int], Optional[str]]:
        normalized_text = text.lower()
        normalized_text_remove_space = self.normalize_vehicle_number(normalized_text)

        vehicle_number = None
        preset_amount = None

        best_match, ratio = process.extractOne(normalized_text_remove_space, self.vehicle_number_list)
        sub_string = ""
        if ratio >= 40:
            vehicle_number = best_match
            match_start = -1
            match_end = -1
            for i in range(len(normalized_text_remove_space) - len(best_match) + 1):
                sub_string = normalized_text_remove_space[i:i + len(best_match)]
                ratio = process.extractOne(sub_string, [best_match])[1]
                if ratio >= 80:
                    match_start = i
                    match_end = i + len(best_match)
                    break

            if match_start != -1:
                normalized_text_remove_space = normalized_text_remove_space[
                                               :match_start] + normalized_text_remove_space[match_end:]

        return vehicle_number, preset_amount, sub_string

    def normalize_vehicle_number(self, vehicle_number: str) -> str:
        return vehicle_number.replace(" ", "").lower()

    def count_vehicle_numbers_with_substring(self, sub_string: str) -> int:
        return sum(1 for vehicle in self.vehicle_number_list if sub_string in vehicle)

    def get_user_conversation_summary(self) -> Optional[str]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        for message in self.conversation_history:
            if message.role == "user":
                potential_vehicle_number, potential_amount, sub_string = self.extract_vehicle_number_and_amount(
                    message.content)
                if potential_vehicle_number:
                    vehicle_number = potential_vehicle_number
                if potential_amount:
                    preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {self.count_vehicle_numbers_with_substring(sub_string)}"
        elif vehicle_number:
            return f", Vehicle number is {vehicle_number}."
        elif preset_amount:
            return ""
        else:
            return ""

    def get_user_vehicle_number(self, response: str) -> Tuple[str, bool]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        if response:
            potential_vehicle_number, potential_amount, sub_string = self.extract_vehicle_number_and_amount(response)
            if potential_vehicle_number:
                vehicle_number = potential_vehicle_number
            if potential_amount:
                preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"my vehicle number is {vehicle_number}, preset amount is {preset_amount}.", True
        elif vehicle_number:
            return f"my vehicle number is {vehicle_number}.", True
        elif preset_amount:
            return response, False
        else:
            return response, False

    def convert_to_json_combine(self, input_string: str) -> dict:
        def parse_value(value):
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value

        input_string = input_string.strip().lower()
        pattern = re.compile(r"\s*(\w+(?: \w+)*?)\s*:\s*'?(.*?)'?\s*(?=,|;|$)")
        matches = pattern.findall(input_string)

        data_dict = {}
        for key, value in matches:
            key = key.strip().replace(' ', '_')
            data_dict[key] = parse_value(value.strip())

        return data_dict


def print_interaction(user_input: str, response: str):
    print(f"User: {user_input}")
    print(f"Assistant: {response}\n")


"""
# Example usage:
vehicle_number_list = ["ABC123", "XYZ789"]
assistant = VehiclePresetAssistant(vehicle_number_list)
assistant.start_new_call()

print_interaction("What vehicles do you have?", assistant.answer_call_back("What vehicles do you have?"))
print_interaction("A B 123 and 100, Sorry, my vehicle number is ABC123.",
                  assistant.answer_call_back("A B 123 and 100, Sorry, my vehicle number is ABC123."))
user_input = "1000"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)
if "completed" in response:
    print("done")
else:
    user_input = "yes"
    response = assistant.answer_call_back(user_input)
    print_interaction(user_input, response)


test_strings = [
    "Can you give me a list of available vehicle numbers?",
    "What vehicles do you have?",
    "Please provide the vehicle numbers.",
    "List all the vehicle numbers.",
    "I need to see the available vehicles.",
    "Show me the vehicle list.",
    "Can I get a list of the vehicles?",
    "What are the vehicle numbers I can choose from?",
    "Provide me with the list of vehicles.",
    "Tell me the available vehicle numbers.",
    "Could you list the vehicle numbers for me?",
    "What vehicle numbers do you have?",
    "Give me the list of vehicle numbers.",
    "I want to know the vehicle numbers.",
    "Please show me the list of vehicles.",
]
for i, test_string in enumerate(test_strings, 1):
    print_interaction("", assistant.start_new_call())
    print_interaction(test_string, assistant.answer_call_back(test_string))
"""

"""
# print(assistant.answer_call_back("I'd like to preset my vehicle ABC123 and preset amount 123"))
print(assistant.answer_call_back("ABC123"))

print(assistant.answer_call_back("123"))

print(assistant.answer_call_back("5000"))

# Test strings
test_strings = [
    "Vehicle number: ap31az8789, preset amount: 10000, output: completed.",
    "Vehicle number: ap 31 az 8789, preset amount: 20000, status: in progress, message: all good.",
    "Vehicle number: 'ap31az8789', preset amount: '10000', output: 'completed'.",
    "Vehicle number: ap31az8789, preset amount: 10000.50, discount: 25, output: completed, approved: True.",
    "Vehicle number: ap31az8789, preset amount: 10000.75, tax rate: 0.18, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, approved: True, output: completed.",
    "Vehicle number: ap31az8789, description: high-end model, preset amount: 10000, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, output: , approved: True.",
    "output: completed.",
    "Vehicle number:ap31az8789,preset amount:10000,output:completed.",
    "Vehicle number: ap31az8789; preset amount: 10000; output: completed.",
    "Vehicle details: {'number': 'ap31az8789', 'model': 'Sedan'}, preset amount: 10000, output: completed.",
    " Vehicle number :  ap31az8789 ,  preset amount :  10000 ,  output :  completed . ",
    "Vehicle number: ap31az8789, preset amount:, output: completed."
]

for i, test_string in enumerate(test_strings, 1):
    result_dict = convert_to_json_combine(test_string)
    print(f"Test Case {i}:\n{json.dumps(result_dict, indent=4)}\n")


conversation_history.append(Message("user", "My vehicle number is ABC 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is a b 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 1 2 3 and I want to preset 5000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 12 34 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 2 34 and I want to preset 50000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is 1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()


# Simulate the conversation with debugging outputs
print("Initial Greeting:")
print(greeting)
print()

vehicle_number_list = ["AB1234", "XYZ789"]
assistant = VehiclePresetAssistant(vehicle_number_list)

user_input = "My vehicle number is AB1234 and I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "can you provide me vehicle list"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "ab1234 and 500"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is ab 1 2 and I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is A123, my vehicle number is ab1234"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 25."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234 and I want to preset 50000."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)
# Start a new call to reset the conversation
print("Starting a new call:")
start_new_call()
print(greeting)
print()

user_input = "My vehicle number is AC1234 and I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AC1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000"
response = answer_call_back(user_input)
print_interaction(user_input, response)
"""
