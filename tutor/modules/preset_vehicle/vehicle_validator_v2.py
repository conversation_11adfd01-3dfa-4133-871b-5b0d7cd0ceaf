import random
import re
from typing import Optional, Tuple, Union, Dict

from api.ongo import ongo_app_api
from executors import static_responses
from modules.logger import logger
from modules.preset_vehicle import vehicle_preset_bot, ags_api_chain
from modules.preset_vehicle.bot import VehiclePresetAssistant
from modules.text import amount


def get_no_vehicle_message():
    messages = [
        "Your vehicle isn't tagged. Please visit the nearest petrol pump to get one. Thank you.",
        "Vehicle not tagged. Please go to the nearest gas station for a tag. Thank you.",
        "Please tag your vehicle at the nearest petrol station and call us again. Thank you.",
        "No tag detected. Visit the closest fuel station for a tag. Thank you.",
        "Tag required. Please get one at the nearest petrol pump. Thank you.",
        "Vehicle tag missing. Head to the nearest petrol station to obtain one. Thank you.",
        "Please visit the nearest gas station to tag your vehicle. Thank you.",
        "Your vehicle needs a tag. Visit the closest petrol pump. Thank you.",
        "Vehicle not tagged. Please get a tag at the nearest petrol station. Thank you.",
        "Please tag your vehicle at the nearest petrol station. Thank you."
    ]
    return random.choice(messages)


class VehicleValidator:
    def __init__(self, user):
        self.user = user
        self.vehicle_number_list = [vehicle['number'].lower() for vehicle in self.user.vehicles]
        self.assistant = VehiclePresetAssistant(mobile=self.user.mobile, session=self.user.session,
                                                full_name=self.user.name,
                                                vehicle_number_list=self.vehicle_number_list, vehicle_validator=self,
                                                loop=self.user.loop)
        self.minimum_amount = 30
        self.maximum_amount = 100000
        self.vehicle_number_str = None

    def start_new_call(self):
        self.assistant.start_new_call()

    import re

    def answer_call_back(self, phrase: str, context: str = None) -> str:
        is_amount = False
        end_call = False
        user_input_final = ''

        # Split the phrase using regular expressions to handle multiple conditions
        sub_phrases = re.split(r'\b\. |\band\.?\s*|\s*and\s*', phrase)

        for sub_sub_phrase in sub_phrases:
            llm_answer = None
            answer_is_not_valid_amount = None
            user_input = ''
            if sub_sub_phrase.strip():
                if self.vehicle_number_str:
                    user_amount_str, is_amount = self.get_amount(sub_sub_phrase)
                    if is_amount:
                        user_input = user_amount_str
                    else:
                        answer_is_not_valid_amount = user_amount_str
                else:
                    vehicle_number_str, is_found_vehicle = self.assistant.get_user_vehicle_number(sub_sub_phrase)
                    if is_found_vehicle:
                        self.vehicle_number_str = vehicle_number_str
                        user_input = vehicle_number_str
                        print("user input modified", user_input)
                        # self.entry_dumper.start_dump_task("modified_user_input", user_input)

                if answer_is_not_valid_amount:
                    print("AI response (user_amount_str):", answer_is_not_valid_amount)
                    return answer_is_not_valid_amount
                else:
                    if user_input:
                        user_input_final += user_input + '. '
                    else:
                        user_input_final += sub_sub_phrase.strip() + '. '
                    # self.entry_dumper.start_dump_task("user_amount_str", llm_answer)

        return self.assistant.answer_call_back(user_input_final.strip(), context)
    

    def answer_call_back_1(self, phrase: str, context: str = None) -> str:

        is_amount = False
        end_call = False
        user_input_final = ''
        # Split the phrase
        phrases = phrase.split(". ")
        # Process each sub-phrase
        for sub_phrase in phrases:
            if sub_phrase:
                sub_phrases = sub_phrase.split("and ")
                for sub_sub_phrase in sub_phrases:
                    llm_answer = None
                    answer_is_not_valid_amount = None
                    user_input = ''
                    if sub_sub_phrase:
                        if self.vehicle_number_str:
                            user_amount_str, is_amount = self.get_amount(sub_sub_phrase)
                            if is_amount:
                                user_input = user_amount_str
                            else:
                                answer_is_not_valid_amount = user_amount_str
                        else:
                            vehicle_number_str, is_found_vehicle = self.assistant.get_user_vehicle_number(
                                sub_sub_phrase)
                            if is_found_vehicle:
                                self.vehicle_number_str = vehicle_number_str
                                user_input = vehicle_number_str
                                print("user input modified", user_input)
                                # self.entry_dumper.start_dump_task("modified_user_input", user_input)

                        if answer_is_not_valid_amount:
                            print("AI response (user_amount_str):", answer_is_not_valid_amount)
                            return answer_is_not_valid_amount
                        else:
                            if user_input:
                                user_input_final += user_input + ' '
                            else:
                                user_input_final += sub_sub_phrase + ' '
                            # self.entry_dumper.start_dump_task("user_amount_str", llm_answer)

        return self.assistant.answer_call_back(user_input_final, context)

    def ai_response_greeting_validation(self, llm_answer: str) -> str:
        phrase = "output completed"
        llm_answer = llm_answer.replace(phrase, "")
        phrase = "output: completed"
        return llm_answer.replace(phrase, "")

    def is_vehicle_number_and_preset_amount_complete(self, llm_answer: str) -> Tuple[
        bool, Optional[Dict], bool, Optional[str], bool, str | None]:
        """
        Check if the vehicle number and preset amount are complete.

        Parameters:
        llm_answer (str): The input string from the LLM.

        Returns:
        tuple: A tuple with a boolean indicating if completed, the JSON object or None,
               a boolean indicating if the phrase was found, and the preset amount check or None.
        """
        phrase = "output: completed"
        is_phrase = phrase in llm_answer
        new_llm_answer = None
        is_end_no_vehicle = False
        if is_phrase:
            try:
                if not self.vehicle_number_list:
                    try:
                        new_llm_answer = llm_answer.replace(phrase, "").replace("  ", " ").strip()
                        cleaned_llm_answer = re.sub(r'[^a-zA-Z0-9\s]', '', new_llm_answer)
                        if not cleaned_llm_answer:
                            is_end_no_vehicle = True
                            new_llm_answer = get_no_vehicle_message()
                    except ValueError:
                        is_end_no_vehicle = True
                        new_llm_answer = get_no_vehicle_message()
                    return False, None, is_phrase, None, is_end_no_vehicle, new_llm_answer

                llm_answer_change = f"mobile_number:{self.user.mobile}, user_id:{self.user.user_id}, {llm_answer}"
                result = self.convert_to_json_combine(llm_answer_change)
                preset_amount = result.get('preset_amount')

                if result.get('vehicle_number') and preset_amount is not None:
                    if preset_amount in ("full", "stop"):
                        preset_amount_check = preset_amount
                    else:
                        preset_amount_check, is_preset_valid = amount.get_amount_from_ai_response(
                            preset_amount, self.minimum_amount, self.maximum_amount, self.vehicle_number_list
                        )
                        if not is_preset_valid:
                            return False, None, is_phrase, preset_amount_check, False, new_llm_answer

                    result['preset_amount'] = preset_amount_check
                    result['vehicle_id'] = self.get_vehicle_id_by_number(result.get('vehicle_number'))
                    return True, result, is_phrase, None, False, new_llm_answer
            except ValueError as e:
                logger.error(f"Error in processing vehicle number and preset amount: {e}")
                return True, None, is_phrase, None, False, new_llm_answer

        return False, None, is_phrase, None, False, new_llm_answer

    def convert_to_json_combine(self, input_string: str) -> dict:
        def parse_value(value):
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value

        input_string = input_string.strip().lower()
        pattern = re.compile(r"\s*(\w+(?: \w+)*?)\s*:\s*'?(.*?)'?\s*(?=,|;|$)")
        matches = pattern.findall(input_string)

        data_dict = {}
        for key, value in matches:
            key = key.strip().replace(' ', '_')
            data_dict[key] = parse_value(value.strip())

        return data_dict

    def get_amount(self, user_input):
        return amount.get_amount_from_user_response(user_input, self.minimum_amount, self.maximum_amount,
                                                    self.vehicle_number_list)

    def get_vehicle_id_by_number(self, vehicle_number: str) -> Optional[str]:
        """Get the vehicle ID by vehicle number."""
        for vehicle in self.user.vehicles:
            if vehicle['number'].lower() == vehicle_number:
                return vehicle['id']
        return None

    def post_vehicle_number_and_preset_amount(self, ai_response: str, result: Optional[Dict], is_phrase: bool) -> str:
        """Post the vehicle number and preset amount to the API."""
        if result:
            is_valid_vehicle, response = self.validate_vehicle(result.get('vehicle_number'))
            if is_valid_vehicle:
                return ongo_app_api.get_response(result)
            else:
                return response
        else:
            if "vehicle" in ai_response and "preset" in ai_response:
                return ai_response
            else:
                return vehicle_preset_bot.get_current_vehicle_preset(self.assistant.conversation_history)

        return ags_api_chain.run(ai_response)

    def validate_vehicle(self, result: str) -> Tuple[bool, str]:
        """Validate the vehicle number."""
        if result in self.vehicle_number_list:
            return True, ""
        elif result == "all":
            return True, ""
        else:
            return False, "error: please provide a valid vehicle number"
