import asyncio
import json
from concurrent.futures import ThreadPoolExecutor

from modules.audio.audio_processor import AudioProcessor
from modules.logger import logger
from modules.preset_vehicle.vehicle_validator import VehicleValidator
from modules.transcript.transcript_manager import TranscriptManager


class User:
    def __init__(self, session: str, name: str, websocket, data: dict, event_loop):
        self.session = session
        self.name = name
        self.conn = websocket
        self.mobile = data.get('mobile')
        self.user_id = data.get('userId')
        self.vehicles = data.get('vehicles', [])
        self.loop = event_loop
        self.is_end_call = False
        self.ai_start_listening = False
        self.executor = ThreadPoolExecutor()

        self.audio_processor = AudioProcessor(self)
        self.transcript_manager = TranscriptManager(self)
        self.vehicle_validator = VehicleValidator(self)

    def __repr__(self):
        return (f"User(session={self.session}, name={self.name}, mobile={self.mobile}, "
                f"user_id={self.user_id}, vehicles={self.vehicles})")

    async def send(self, message: str):
        """Send a message to the user."""
        try:
            await self.conn.send(message)
        except Exception as e:
            logger.error(f"Failed to send message: {e}")

    def sync_send(self, message: str):
        """Send a message to the user synchronously."""
        asyncio.run_coroutine_threadsafe(self.send(message), self.loop)

    def start_ai_call(self):
        """Start AI call for the user."""
        self.audio_processor.start_ai_call()

    def end_ai_call(self):
        """End AI call for the user and save audio file."""
        self.audio_processor.end_ai_call()

    def send_end_call(self):
        """Notify the user that the AI call has ended and close the connection."""
        self.is_end_call = True
        self.sync_send(json.dumps({"type": "ai_end_call", "data": "ai call completed"}))
        self._run_async(self.conn.close())
        self.audio_processor.save_audio_file()

    def _run_async(self, coroutine):
        """Run an asynchronous coroutine."""
        asyncio.run_coroutine_threadsafe(coroutine, self.loop)
