import asyncio
import io
import os
import re
import time
from collections import deque
from concurrent.futures import Thread<PERSON><PERSON>Executor
from threading import Thread

from pydub import AudioSegment
from speech_recognition import Recognizer, RequestError, UnknownValueError, AudioData


class AudioRecognizer:
    def __init__(self, user, max_workers=4):
        self.user = user
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.loop = asyncio.get_event_loop()

        # Ensure user and session directories exist
        self.base_dir = 'audio_sessions'
        self.user_dir = os.path.join(self.base_dir, self.user.mobile)
        os.makedirs(self.user_dir, exist_ok=True)
        self.session_dir = os.path.join(self.user_dir, self.user.session)
        os.makedirs(self.session_dir, exist_ok=True)

        self.full_audio_buffer = io.BytesIO()

    def recognize_audio(self, audio_bytes):
        audio_content = AudioData(audio_bytes, sample_rate=16000, sample_width=2)
        try:
            lc_recognizer = Recognizer()
            lc_recognizer.operation_timeout = 2
            recognized_text = lc_recognizer.recognize_google(audio_content)
            # Remove unwanted characters (non-alphanumeric except spaces)
            cleaned_text = re.sub(r'[^a-zA-Z0-9\s]', '', recognized_text)
            # Convert to lowercase
            cleaned_text = cleaned_text.lower()
            # Remove extra spaces
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
            print(f"Recognized text: {cleaned_text}")
            return cleaned_text
        except UnknownValueError:
            print("Google Speech Recognition could not understand audio")
            return None
        except RequestError as e:
            print(f"Could not request results from Google Speech Recognition service; {e}")
            return None
        except Exception as e:
            print(f"An error occurred: {e}")
            return None

    async def listen_and_recognize(self):
        results = []
        while True:
            try:
                await asyncio.sleep(self.user.pause)
                audio_buffer = io.BytesIO()

                while self.user.audio_queue:
                    audio_chunk = self.user.audio_queue.popleft()
                    audio_buffer.write(audio_chunk)
                    self.full_audio_buffer.write(audio_chunk)
                # Process the audio data in a separate thread
                print("Processing audio data...")
                audio_buffer.seek(0)
                audio_bytes = audio_buffer.read()

                start_time = time.time()
                transcript = await self.loop.run_in_executor(self.executor, self.recognize_audio, audio_bytes)
                end_time = time.time()
                response_time = end_time - start_time
                print("Response time of recognize_audio:", response_time)

                # Save the audio file
                Thread(target=self.save_audio_file).start()

                if transcript:
                    print("Transcript generated:", transcript)
                    self.user.transcripts.append(transcript)
                    results.append(transcript)
                    self.user.pause = 2
                elif transcript is None and len(results) >= 1:
                    break
                elif transcript is None:
                    break

            except ValueError:
                break

        try:
            if len(results) == 0:
                return None
            return ' '.join(results).lower()
        except Exception as e:
            print(f"An error occurred while generating the final result: {e}")
            return None

    def save_audio_file(self):
        self.full_audio_buffer.seek(0)
        audio_bytes = self.full_audio_buffer.read()

        # Convert raw PCM data to WAV format using pydub
        audio_segment = AudioSegment(
            data=audio_bytes,
            sample_width=2,  # 16-bit PCM
            frame_rate=16000,  # Sample rate
            channels=1  # Mono
        )
        # Save the audio data to a .wav file
        audio_file_path = os.path.join(self.session_dir, f"{self.user.session}.wav")
        audio_segment.export(audio_file_path, format="wav")

        print(f"Audio file saved: {audio_file_path}")


# Example usage
async def main():
    user = {
        "pause": 2,
        "audio_queue": deque(),
        "transcripts": []
    }

    recognizer = AudioRecognizer(user)
    result = await recognizer.listen_and_recognize()
    print("Final result:", result)


# To run the main function
if __name__ == "__main__":
    asyncio.run(main())
