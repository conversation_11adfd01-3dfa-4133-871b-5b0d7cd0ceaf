import asyncio
import io
import os
import re
import threading
import time
import wave
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from typing import Deque, Any, Generator

from pydub import AudioSegment
from pydub.silence import split_on_silence
import numpy as np
import webrtcvad

from speech_recognition import <PERSON>cognizer, Request<PERSON>rror, Unknown<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AudioD<PERSON>

from tutor.executors import files
from tutor.modules.audio.assembly_ai.realtime_transcription import RealtimeTranscription

class AudioProcessor:
    def __init__(self, user, logger, max_workers=4):
        self.user = user
        self.logger = logger
        self.session = user.session
        self.mobile = user.mobile
        self.audio_queue = deque()
        self.llm_audio_buffer = io.BytesIO()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.loop = user.loop  # asyncio.get_event_loop()
        # self.tts = TextToSpeech()
        # self.tts = TextToSpeech(voice_type="coqui")
        # Ensure user and session directories exist
        # self.base_dir = 'audio_sessions'
        # self.user_dir = os.path.join(self.base_dir, self.user.mobile)
        # os.makedirs(self.user_dir, exist_ok=True)
        # self.session_dir = os.path.join(self.user_dir, self.user.session)
        # os.makedirs(self.session_dir, exist_ok=True)

        self.full_audio_buffer = io.BytesIO()
        self.vad = webrtcvad.Vad(3)  # Set aggressiveness mode (0-3)
        self.silence_timeout = 0.4  # seconds of silence before considering speech ended

        # Start the background task
        self.is_listen_monitor = False
        self.listen_task = asyncio.create_task(self._listen_monitor())
        self.user_speaking = False
        self.temp_audio_buffer = io.BytesIO()

    def start_ai_call(self):
        self.is_listen_monitor = True
        self.listen_task = asyncio.create_task(self._listen_monitor())
        threading.Thread(target=self._accumulate_and_process_audio, daemon=True).start()

    def _accumulate_and_process_audio(self):
        self.user.realtime_transcription = RealtimeTranscription("25c5df29759f4b4a99d54a3a10d60b98", user=self.user, logger=self.logger)
        self.user.realtime_transcription.connect()
        while True:
            try:
                self.user.realtime_transcription.start()
                break
            except Exception as e:
                self.logger.error(f"Error during realtime transcription: {e}")
                break

    def _recognize_audio(self, audio_bytes):
        audio_content = AudioData(audio_bytes, sample_rate=16000, sample_width=2)
        try:
            lc_recognizer = Recognizer()
            recognized_text = lc_recognizer.recognize_google(audio_content)
            # cleaned_text = self.clean_text(recognized_text)
            self.logger.info(f"Recognized text: {recognized_text}")
            return recognized_text
        except UnknownValueError:
            self.logger.error("Google Speech Recognition could not understand audio")
            return None
        except RequestError as e:
            self.logger.error(f"Could not request results from Google Speech Recognition service; {e}")
            return None
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return None

    def _recognize_audio_open_ai(self, audio_bytes):
        with wave.open("temp.wav", 'wb') as wf:
            wf.setnchannels(1)  # Assuming mono audio
            wf.setsampwidth(2)  # Assuming 16-bit audio
            wf.setframerate(16000)  # Assuming 16000 Hz sample rate
            wf.writeframes(audio_bytes)
        try:
            with open("temp.wav", "rb") as audio_file:
                transcription = self.user.client.audio.translations.create(
                    model="whisper-1",
                    file=audio_file
                )
                response = transcription.text
                cleaned_text = self.clean_text(response)
                self.logger.info(f"Recognized text: {cleaned_text}")
                return cleaned_text
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return None

    def clean_text(self, text):
        cleaned_text = re.sub(r'[^a-zA-Z0-9\s]', '', text)
        cleaned_text = cleaned_text.lower()
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        return cleaned_text

    def is_speech(self, audio_chunk):
        frame_duration_ms = 30
        frame_size = int(16000 * (frame_duration_ms / 1000.0) * 2)
        if len(audio_chunk) != frame_size:
            return self.user_speaking
        return self.vad.is_speech(audio_chunk, sample_rate=16000)

    async def _listen_monitor(self):
        while self.is_listen_monitor:
            try:
                await asyncio.sleep(0.24)
                audio_buffer = io.BytesIO()
                while self.audio_queue:
                    audio_chunk = self.audio_queue.popleft()
                    audio_buffer.write(audio_chunk)
                    self.temp_audio_buffer.write(audio_chunk)
                    self.full_audio_buffer.write(audio_chunk)
                audio_buffer.seek(0)
                audio_bytes = audio_buffer.read()
                if self.user.realtime_transcription:
                    self.user.realtime_transcription.audio_queue.append(audio_bytes)
            except ValueError as e:
                self.logger.error(f"ValueError encountered: {e}")
                break

    async def listen_and_recognize(self):
        results = []
        while self.user_speaking:
            await asyncio.sleep(0.01)
        try:
            self.temp_audio_buffer.seek(0)
            audio_bytes = self.temp_audio_buffer.read()
            self.logger.info("Processing audio data...")
            reduced_noise = self._noise_reduction(audio_bytes, rate=16000)
            vad_data = self._vad_process(reduced_noise, rate=16000)
            self.temp_audio_buffer = io.BytesIO()
            start_time = time.time()
            transcript = await self.loop.run_in_executor(self.executor, self._recognize_audio, vad_data.tobytes())
            end_time = time.time()
            response_time = end_time - start_time
            self.logger.info(f"Response time of recognize_audio:{response_time}")
            if transcript:
                self.logger.info("Transcript generated:", transcript)
                self.user.transcripts.append(transcript)
                results.append(transcript)
            if len(results) == 0:
                return None
            return ' '.join(results).lower()
        except Exception as e:
            self.logger.error(f"An error occurred while generating the final result: {e}")
            return None

    def get_speech_to_text(self)->str:
        while self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                self.full_audio_buffer.write(audio_chunk)
        self.full_audio_buffer.seek(0)
        audio_bytes = self.full_audio_buffer.read()
        self.full_audio_buffer = io.BytesIO()
        return self._recognize_audio(audio_bytes)


    def save_audio_file(self):
        self.is_listen_monitor = False
        if self.user.realtime_transcription:
            self.user.realtime_transcription.close()
        self.full_audio_buffer.seek(0)
        audio_bytes = self.full_audio_buffer.read()
        # reduced_noise = self._noise_reduction(audio_bytes, rate=16000)
        # vad_data = self._vad_process(reduced_noise, rate=16000)
        files.save_audio_segment(data=audio_bytes, mobile=self.mobile, session=self.session)
        # self.llm_audio_buffer.seek(0)
        # llm_audio_bytes = self.llm_audio_buffer.read()
        # self._mix_and_save_audio(llm_audio_bytes, audio_bytes)

    def _extend_audio_to_match_length(self, shorter: AudioSegment, length: int) -> AudioSegment:
        """Extends the shorter audio to match the given length by repeating."""
        extended = AudioSegment.empty()
        while len(extended) < length:
            extended += shorter
        return extended[:length]

    def _convert_bytes_to_audio_segment(self, audio_bytes: bytes) -> AudioSegment | None:
        try:
            audio_segment = AudioSegment(
                data=audio_bytes,
                sample_width=2,  # 16-bit PCM
                frame_rate=16000,  # Sample rate
                channels=1  # Mono
            )
            return audio_segment
        except Exception as e:
            self.logger.error(f"Error converting bytes to AudioSegment: {e}")
            return None

    def _mix_and_save_audio(self, llm_audio_bytes: bytes, audio_bytes: bytes) -> None:
        """Mixes two audio streams from queues and saves the result."""
        try:
            audio1 = self._convert_bytes_to_audio_segment(llm_audio_bytes)
            audio2 = self._convert_bytes_to_audio_segment(audio_bytes)
        except Exception as e:
            self.logger.error(f"Error converting audio bytes to AudioSegments: {e}")
            return

        # Extend the shorter audio file to match the length of the longer one
        if len(audio1) > len(audio2):
            audio2 = self._extend_audio_to_match_length(audio2, len(audio1))
        else:
            audio1 = self._extend_audio_to_match_length(audio1, len(audio2))

        # Mix the audio files
        mixed = audio1.overlay(audio2)

        # Export the mixed audio to a bytes buffer
        mixed_audio_buffer = io.BytesIO()
        mixed.export(mixed_audio_buffer, format="wav")
        mixed_audio_buffer.seek(0)

        # Save the mixed audio segment
        files.save_audio_segment(data=mixed_audio_buffer.read(), mobile=self.mobile, session=self.session)

    def _noise_reduction(self, data, rate):
        data = np.frombuffer(data, dtype=np.int16)
        if len(data.shape) == 1:
            data = data.reshape(-1)
        if len(data) < rate * 0.1:
            raise ValueError("Not enough audio data to process.")
        audio_segment = AudioSegment(
            data=data.tobytes(),
            sample_width=2,  # Assuming 16-bit PCM
            frame_rate=rate,
            channels=1  # Mono audio
        )
        chunks = split_on_silence(audio_segment, min_silence_len=200, silence_thresh=-32)
        reduced_audio = AudioSegment.silent(duration=0)
        for chunk in chunks:
            reduced_audio += chunk
        return np.array(reduced_audio.get_array_of_samples())

    def _vad_process(self, data, rate, frame_duration=30):
        vad = webrtcvad.Vad()
        vad.set_mode(1)
        frames = []
        num_frames = int(rate * frame_duration / 1000)
        self.logger.info(f"Processing VAD with num_frames: {num_frames}, total data length: {len(data)}")
        for i in range(0, len(data) - num_frames + 1, num_frames):
            frame = data[i:i + num_frames]
            is_speech = vad.is_speech(frame.tobytes(), rate)
            if is_speech:
                frames.append(frame)
        if frames:
            concatenated_frames = np.concatenate(frames)
            self.logger.info(f"Number of speech frames: {len(frames)}, Concatenated frames length: {len(concatenated_frames)}")
            return concatenated_frames
        else:
            self.logger.info("No speech frames detected.")
            return np.array([])  # Return an empty array if no speech frames are detected
