import asyncio
import base64
import json
import threading
import time
from collections import deque
import assemblyai as aai  # Assuming aai is the relevant module for the transcription API
import io

from modules.text import word_to_number


class RealtimeTranscription:
    def __init__(self, api_key, user, sample_rate=16000):
        self.user = user
        self.api_key = api_key
        self.sample_rate = sample_rate
        self.audio_queue = deque()
        self.listen_task_threading = threading.Thread(target=self.stream_audio_data, daemon=True)
        self.is_stream_audio_data = True
        self.realtime_final_transcript = None
        # Setting the API key
        aai.settings.api_key = self.api_key
        # Initialize the transcriber
        self.transcriber = aai.RealtimeTranscriber(
            sample_rate=self.sample_rate,
            on_data=self.on_data,
            on_error=self.on_error,
            on_open=self.on_open,
            on_close=self.on_close,
        )

        self.is_completed = True

    def on_open(self, session_opened: aai.RealtimeSessionOpened):
        print("Session ID:", session_opened.session_id)

    def on_data(self, transcript: aai.RealtimeTranscript):
        if not transcript.text:
            return

        if isinstance(transcript, aai.RealtimeFinalTranscript):
            print(f"{aai.RealtimeFinalTranscript}: {transcript.text}", end="\r\n")
            if self.is_completed:
                self.handle_final_transcript_from_transcription(transcript.text)
        else:
            print(f"transcript: {transcript.text}", end="\r")
            self.user.handle_partial_transcript_from_transcription(transcript.text)

    def on_error(self, error: aai.RealtimeError):
        print("An error occurred:", error)

    def on_close(self):
        print("Closing Session")

    def connect(self):
        self.transcriber.connect()

    def close(self):
        self.is_stream_audio_data = False
        self.transcriber.close()
        # self.listen_task_threading.join()

    def add_audio_chunk(self, audio_chunk):
        self.audio_queue.append(audio_chunk)

    def simulate_audio_data(self):
        import time
        while True:
            # Simulate audio chunk (replace this with actual audio data)
            audio_chunk = b'\x00' * self.sample_rate
            self.add_audio_chunk(audio_chunk)
            time.sleep(1)

    def stream_audio_data(self):
        audio_buffer = io.BytesIO()
        while self.is_stream_audio_data:
            if self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                audio_buffer.write(audio_chunk)

                audio_buffer.seek(0)
                audio_bytes = audio_buffer.read()
                self.transcriber.stream(audio_bytes)

                # Clear the buffer for the next set of audio chunks
                audio_buffer.seek(0)
                audio_buffer.truncate(0)
            else:
                time.sleep(0.01)  # Add a short sleep to prevent busy waiting

    def start(self):
        # Start simulating audio data in a separate thread
        self.stream_audio_data()

    def handle_final_transcript_from_transcription(self, user_input):
        while True:
            self.is_completed = False
            user_input = word_to_number.remove_symbols_except_period(user_input)
            self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))
            print("Pausing audio recording...")
            self.user.sync_send(json.dumps({"type": "pause_audio_recording", "data": ""}))
            # user_input = user.get_transcript()
            print("user input:", user_input)
            is_amount = False
            llm_answer = None
            start_time = time.time()
            vehicle_number_str = self.user.vehicle_number_str
            if vehicle_number_str:
                user_amount_str, is_amount = self.user.get_amount(user_input)
                if is_amount:
                    user_input = user_amount_str
                else:
                    llm_answer = user_amount_str
            else:
                vehicle_number_str, is_found_vehicle = self.user.assistant.get_user_vehicle_number(user_input)
                if is_found_vehicle:
                    self.user.vehicle_number_str = vehicle_number_str
                    user_input = vehicle_number_str

            if llm_answer:
                print("ai response:", llm_answer)
            else:
                llm_answer = self.user.assistant.answer_call_back(user_input)
                end_time = time.time()
                response_time = end_time - start_time
                print("Response time of bot.answer_call_back:", response_time)
                print("ai response:", llm_answer)

                status, result, is_phrase, preset_amount_response = self.user.is_vehicle_number_and_preset_amount_complete(
                    llm_answer)
                if status:
                    self.text_to_audio_chunk_send(self.user.assistant.sending_user_request())
                    llm_answer = self.user.post_vehicle_number_and_preset_amount(llm_answer, result, is_phrase)
                    if "error:" in llm_answer:
                        llm_answer = llm_answer.replace("error:", "")
                    else:
                        self.user.send_end_call()
                elif preset_amount_response:
                    llm_answer = preset_amount_response

            print("ai after validation response:", llm_answer)
            self.text_to_audio_chunk_send(llm_answer)
            self.is_completed = True
            break

    def text_to_audio_chunk_send(self, text):
        def send_audio():
            self.user.ai_start_listening = False
            self.user.audio_chunk_is_send = False

            audio_stream = self.user.tts.generate_audio(text)  # Convert text to audio stream
            buffer = io.BytesIO()

            audio_chunk_size = 256  # Smaller chunk size for reduced latency

            while True:
                audio_chunk = audio_stream.read(audio_chunk_size)  # Read audio data in smaller chunks
                if not audio_chunk:
                    break
                buffer.write(audio_chunk)

                if buffer.tell() >= audio_chunk_size:
                    buffer.seek(0)
                    audio_chunk_base64 = base64.b64encode(buffer.read(audio_chunk_size)).decode('utf-8')
                    self.user.sync_send(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))
                    buffer.seek(0)
                    buffer.truncate(0)

            # Send remaining data in buffer
            if buffer.tell() > 0:
                buffer.seek(0)
                audio_chunk_base64 = base64.b64encode(buffer.read()).decode('utf-8')
                self.user.sync_send(json.dumps({"type": "llm_answer", "data": audio_chunk_base64}))

            self.user.audio_chunk_is_send = True

        # Start send_audio in a separate thread
        send_audio_thread = threading.Thread(target=send_audio)
        send_audio_thread.start()
        # Wait for send_audio thread to finish
        send_audio_thread.join()


# Example usage:
# transcription = RealtimeTranscription(api_key="your_api_key", user="your_user_id")
# transcription.connect()
# transcription.start()
# final_transcript = transcription.get_transcript()
# print("Final transcript:", final_transcript)
