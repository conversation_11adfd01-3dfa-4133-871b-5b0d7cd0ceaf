import asyncio
import io
import re
import time
from concurrent.futures import ThreadPoolExecutor

from speech_recognition import (<PERSON><PERSON><PERSON><PERSON>, RequestError,
                                UnknownValueError, AudioData)


def recognize_audio(audio_bytes):
    audio_content = AudioData(audio_bytes, sample_rate=16000, sample_width=2)
    try:
        lc_recognizer = Recognizer()
        lc_recognizer.operation_timeout = 2
        recognized_text = lc_recognizer.recognize_google(audio_content)
        # Remove unwanted characters (non-alphanumeric except spaces)
        cleaned_text = re.sub(r'[^a-zA-Z0-9\s]', '', recognized_text)
        # Convert to lowercase
        cleaned_text = cleaned_text.lower()
        # Remove extra spaces
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        print(f"Recognized text: {cleaned_text}")
        return cleaned_text
    except UnknownValueError:
        print("Google Speech Recognition could not understand audio")
        return None
    except RequestError as e:
        print(f"Could not request results from Google Speech Recognition service; {e}")
        return None
    except Exception as e:
        return None


async def listen_and_recognize(user):
    results = []
    executor = ThreadPoolExecutor(max_workers=4)  # Adjust the number of workers as needed
    while True:
        try:
            await asyncio.sleep(user["pause"])
            audio_buffer = io.BytesIO()

            while user["audio_queue"]:
                audio_chunk = user["audio_queue"].popleft()
                audio_buffer.write(audio_chunk)

            # Process the audio data in a separate thread
            print("Processing audio data...")
            audio_buffer.seek(0)
            audio_bytes = audio_buffer.read()
            loop = asyncio.get_event_loop()
            start_time = time.time()
            transcript = await loop.run_in_executor(executor, recognize_audio, audio_bytes)
            end_time = time.time()
            response_time = end_time - start_time
            print("Response time of recognize_audio:", response_time)

            if transcript:
                print("Transcript generated:", transcript)
                user["transcripts"].append(transcript)
                results.append(transcript)
                user["pause"] = 2
            elif transcript is None and len(results) >= 1:
                break
            elif transcript is None:
                break

        except ValueError:
            break

    try:
        if len(results) == 0:
            return None
        return ' '.join(results).lower()
    except Exception as e:
        return None
