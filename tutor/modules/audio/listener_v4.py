import asyncio
import io
import os
import re
import time
import wave
from collections import deque
from concurrent.futures import ThreadPoolExecutor

import numpy as np
import webrtcvad
from openai import OpenAI
from pydub import AudioSegment
from pydub.silence import split_on_silence
from speech_recognition import Recognizer, Request<PERSON><PERSON>r, Unknown<PERSON><PERSON>ueError, AudioData


class AudioRecognizer:
    def __init__(self, user, max_workers=4):
        self.user = user
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.loop = asyncio.get_event_loop()

        # Ensure user and session directories exist
        self.base_dir = 'audio_sessions'
        self.user_dir = os.path.join(self.base_dir, self.user.mobile)
        os.makedirs(self.user_dir, exist_ok=True)
        self.session_dir = os.path.join(self.user_dir, self.user.session)
        os.makedirs(self.session_dir, exist_ok=True)

        self.full_audio_buffer = io.BytesIO()
        self.vad = webrtcvad.Vad(3)  # Set aggressiveness mode (0-3)
        self.silence_timeout = 0.6  # seconds of silence before considering speech ended

        # Start the background task
        self.is_listen_monitor = True
        self.listen_task = asyncio.create_task(self.listen_monitor())
        self.user_speaking = False
        self.temp_audio_buffer = io.BytesIO()
        self.client = OpenAI()

    def recognize_audio(self, audio_bytes):
        audio_content = AudioData(audio_bytes, sample_rate=16000, sample_width=2)
        try:
            lc_recognizer = Recognizer()
            # lc_recognizer.operation_timeout = 2
            recognized_text = lc_recognizer.recognize_google(audio_content)
            # Remove unwanted characters (non-alphanumeric except spaces)
            cleaned_text = re.sub(r'[^a-zA-Z0-9\s]', '', recognized_text)
            # Convert to lowercase
            cleaned_text = cleaned_text.lower()
            # Remove extra spaces
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
            print(f"Recognized text: {cleaned_text}")
            return cleaned_text
        except UnknownValueError:
            print("Google Speech Recognition could not understand audio")
            return None
        except RequestError as e:
            print(f"Could not request results from Google Speech Recognition service; {e}")
            return None
        except Exception as e:
            print(f"An error occurred: {e}")
            return None

    def recognize_audio_open_ai(self, audio_bytes):
        # Save audio data to a temporary WAV file
        with wave.open("temp.wav", 'wb') as wf:
            wf.setnchannels(1)  # Assuming mono audio
            wf.setsampwidth(2)  # Assuming 16-bit audio
            wf.setframerate(16000)  # Assuming 16000 Hz sample rate
            wf.writeframes(audio_bytes)
        try:
            # Transcribe audio file using OpenAI's Whisper model
            with open("temp.wav", "rb") as audio_file:
                transcription = self.client.audio.translations.create(
                    model="whisper-1",
                    file=audio_file
                )

                response = transcription.text
                # Remove unwanted characters (non-alphanumeric except spaces)
                cleaned_text = re.sub(r'[^a-zA-Z0-9\s]', '', response)
                # Convert to lowercase
                cleaned_text = cleaned_text.lower()
                # Remove extra spaces
                cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
                print(f"Recognized text: {cleaned_text}")
                return cleaned_text
        except UnknownValueError:
            print("Google Speech Recognition could not understand audio")
            return None
        except RequestError as e:
            print(f"Could not request results from Google Speech Recognition service; {e}")
            return None
        except Exception as e:
            print(f"An error occurred: {e}")
            return None

    def is_speech(self, audio_chunk):
        """Check if the given audio chunk contains speech."""
        # WebRTC VAD expects a specific frame size in bytes
        frame_duration_ms = 30  # You can use 10, 20, or 30 ms
        frame_size = int(16000 * (frame_duration_ms / 1000.0) * 2)  # sample_rate * duration * bytes_per_sample

        # Ensure the audio_chunk is in correct frame size
        if len(audio_chunk) != frame_size:
            raise ValueError(f"Audio chunk size {len(audio_chunk)} does not match expected frame size {frame_size}")
        return self.vad.is_speech(audio_chunk, sample_rate=16000)

    async def listen_monitor(self):
        results = []
        last_speech_time = time.time()
        frame_duration_ms = 30  # Frame duration for VAD in milliseconds
        frame_size = int(16000 * (frame_duration_ms / 1000.0) * 2)  # Calculate frame size

        while self.is_listen_monitor:
            try:
                await asyncio.sleep(0.24)
                audio_buffer = io.BytesIO()
                while self.user.audio_queue:
                    audio_chunk = self.user.audio_queue.popleft()
                    audio_buffer.write(audio_chunk)
                    self.temp_audio_buffer.write(audio_chunk)
                    self.full_audio_buffer.write(audio_chunk)

                audio_buffer.seek(0)
                audio_bytes = audio_buffer.read()

                # Split audio buffer into frames of the appropriate size
                for i in range(0, len(audio_bytes), frame_size):
                    frame = audio_bytes[i:i + frame_size]
                    if len(frame) == frame_size:
                        if self.is_speech(frame):
                            # print("User started speaking.")
                            self.user_speaking = True
                            last_speech_time = time.time()

                # Check if the user has stopped speaking
                if self.user_speaking and time.time() - last_speech_time > self.silence_timeout:
                    print("User has stopped speaking.")
                    self.user_speaking = False


            except ValueError as e:
                print(f"ValueError encountered: {e}")
                break

    def noise_reduction(self, data, rate):
        # Convert byte data to numpy array
        data = np.frombuffer(data, dtype=np.int16)

        # Ensure the data is a 1D array
        if len(data.shape) == 1:
            data = data.reshape(-1)

        # Check if data has enough frames for processing
        if len(data) < rate * 0.1:  # Less than 0.1 seconds of data
            raise ValueError("Not enough audio data to process.")

        # Convert numpy array to pydub AudioSegment
        audio_segment = AudioSegment(
            data=data.tobytes(),
            sample_width=2,  # Assuming 16-bit PCM
            frame_rate=rate,
            channels=1  # Mono audio
        )

        # Split audio into chunks at silence points
        chunks = split_on_silence(audio_segment, min_silence_len=200, silence_thresh=-32)

        # Perform basic noise reduction by concatenating non-silent chunks
        reduced_audio = AudioSegment.silent(duration=0)
        for chunk in chunks:
            reduced_audio += chunk

        return np.array(reduced_audio.get_array_of_samples())

    def vad_process(self, data, rate, frame_duration=30):
        vad = webrtcvad.Vad()
        vad.set_mode(1)  # 0: Aggressive, 3: Very Aggressive
        frames = []
        num_frames = int(rate * frame_duration / 1000)

        for i in range(0, len(data) - num_frames + 1, num_frames):
            frame = data[i:i + num_frames]
            is_speech = vad.is_speech(frame.tobytes(), rate)
            if is_speech:
                frames.append(frame)

        return np.concatenate(frames)

    async def listen_and_recognize(self):
        results = []
        while self.user_speaking:
            await asyncio.sleep(0.01)

        try:
            self.temp_audio_buffer.seek(0)
            audio_bytes = self.temp_audio_buffer.read()
            # Process the audio data in a separate thread
            print("Processing audio data...")
            # Apply noise reduction
            reduced_noise = self.noise_reduction(audio_bytes, rate=16000)

            # Apply VAD
            vad_data = self.vad_process(reduced_noise, rate=16000)

            self.temp_audio_buffer = io.BytesIO()
            start_time = time.time()
            transcript = await self.loop.run_in_executor(self.executor, self.recognize_audio, vad_data.tobytes())
            end_time = time.time()
            response_time = end_time - start_time
            print("Response time of recognize_audio:", response_time)

            if transcript:
                print("Transcript generated:", transcript)
                self.user.transcripts.append(transcript)
                results.append(transcript)
                # self.user.pause = 2
            elif transcript is None and len(results) >= 1:
                # break
                print("value")
            elif transcript is None:
                # break
                print("value1")

            if len(results) == 0:
                return None
            return ' '.join(results).lower()
        except Exception as e:
            print(f"An error occurred while generating the final result: {e}")
            return None

    async def save_audio_file(self):
        self.is_listen_monitor = False
        self.full_audio_buffer.seek(0)
        audio_bytes = self.full_audio_buffer.read()

        # Apply noise reduction
        reduced_noise = self.noise_reduction(audio_bytes, rate=16000)

        # Apply VAD
        vad_data = self.vad_process(reduced_noise, rate=16000)

        # Convert raw PCM data to WAV format using pydub
        audio_segment = AudioSegment(
            data=vad_data.tobytes(),
            sample_width=2,  # 16-bit PCM
            frame_rate=16000,  # Sample rate
            channels=1  # Mono
        )
        # Save the audio data to a .wav file
        audio_file_path = os.path.join(self.session_dir, f"{self.user.session}.wav")
        audio_segment.export(audio_file_path, format="wav")

        print(f"Audio file saved: {audio_file_path}")


# Example usage
async def main():
    user = {
        "pause": 2,
        "audio_queue": deque(),
        "transcripts": []
    }

    recognizer = AudioRecognizer(user)
    result = await recognizer.listen_and_recognize()
    print("Final result:", result)


# To run the main function
if __name__ == "__main__":
    asyncio.run(main())
