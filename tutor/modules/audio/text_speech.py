import asyncio
import hashlib
import os
import re
import unittest
from pathlib import Path
from difflib import SequenceMatcher
from typing import Optional, Tuple, Union

import requests
from dotenv import load_dotenv
from gtts import gTTS
from pydub import AudioSegment
import io
import pyaudio
import openai
import re
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor

from tutor.modules.exceptions import EgressErrors
from tutor.modules.models import models
from tutor.modules.logger import logger


async def speech_synthesizer(text: str,
                       timeout: Union[int, float] = None,
                       quality: str = models.env.speech_synthesis_quality,
                       voice: str = models.env.speech_synthesis_voice,
                       filename: str = models.fileio.speech_synthesis_wav) -> bool:
    """Generates audio for the provided text using a TTS engine and saves it to a file.

    Args:
        text: The text to be synthesized into speech.
        timeout: Optional timeout for processing.
        quality: The quality setting for speech synthesis.
        voice: The selected voice for speech synthesis.
        filename: The path to save the synthesized audio file.

    Returns:
        bool: True if audio was successfully generated and saved, False otherwise.
    """
    logger.info("Request for speech synthesis: %s", text)
    # Preprocess the text
    text = text.replace("%", " percent")
    if time_in_str := re.findall(r'(\d+:\d+\s?(?:AM|PM|am|pm:?))', text):
        for t_12 in time_in_str:
            t_24 = datetime.strftime(datetime.strptime(t_12, "%I:%M %p"), "%H:%M")
            logger.info("Converted %s -> %s", t_12, t_24)
            text = text.replace(t_12, t_24)
    if 'IP' in text.split():
        ip_new = '-'.join([i for i in text.split(' ')[-1]]).replace('-.-', ', ')  # *********** -> 1-9-2, 1-6-8, 1, 1
        text = text.replace(text.split(' ')[-1], ip_new).replace(' IP ', ' I.P. ')
    
    # Handle degree symbols for Fahrenheit and Celsius
    text = text.replace("\N{DEGREE SIGN}F", " degrees fahrenheit")
    text = text.replace("\N{DEGREE SIGN}C", " degrees celsius")

    try:
        # Generate audio asynchronously using the TextToSpeech instance
        audio_stream, audio_stream_1 = await asyncio.to_thread(tts_instance.generate_audio, text)
        
        # Save the second audio stream to a file
        if audio_stream_1:
            with open(file=filename, mode="wb") as file:
                file.write(audio_stream_1.read())
                file.flush()
            logger.info("Audio saved to %s", filename)
            return True
        
        logger.error("Failed to generate speech synthesis for text: %s", text)
        return False
    
    except UnicodeError as error:
        logger.error("Unicode error in speech synthesis: %s", error)
    except EgressErrors as error:
        logger.error("Egress error in speech synthesis: %s", error)
        logger.info("Disabling speech synthesis")
        if not any((isinstance(error, TimeoutError), isinstance(error, requests.Timeout))):
            models.env.speech_synthesis_timeout = 0

    return False
         
class TextToSpeech:
    _instance = None
    _executor = ThreadPoolExecutor(max_workers=1)

    def __new__(cls, voice_type="google", speaker_id=""):
        if cls._instance is None:
            cls._instance = super(TextToSpeech, cls).__new__(cls)
            load_dotenv()
            cls._instance.api_key = os.getenv('OPENAI_API_KEY')
            # openai.api_key = cls.api_key
            cls._instance.client = openai
            cls._instance.speaker_id = speaker_id
            # cls._instance.audio_dir = Path(__file__).parent / f"audio_file_{voice_type}_{speaker_id}"
            cls._instance.audio_dir = Path(os.path.join(models.fileio.root, f"audio_file_{voice_type}_{speaker_id}"))
            cls._instance.audio_dir.mkdir(exist_ok=True)
            cls._instance.voice_type = voice_type
            cls._instance.logger = logging.getLogger(__name__)
            cls._instance.audio_cache = {}
            cls._instance._executor.submit(cls._instance._load_audio_cache)
        return cls._instance

    def _load_audio_cache(self):
        # Load existing audio files into cache
        self.logger.info("Starting to load audio cache in the background.")
        for audio_file in self.audio_dir.glob("*.mp3"):
            text_hash = audio_file.stem
            audio_segment = AudioSegment.from_file(audio_file, format="mp3")

            pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)
            pcm_audio_1 = audio_segment.set_frame_rate(16000).set_channels(1).set_sample_width(2)

            pcm_audio_stream = io.BytesIO()
            pcm_audio.export(pcm_audio_stream, format="raw")
            pcm_audio_stream.seek(0)

            pcm_audio_stream_1 = io.BytesIO()
            pcm_audio_1.export(pcm_audio_stream_1, format="wav")
            pcm_audio_stream_1.seek(0)

            self.audio_cache[text_hash] = (pcm_audio_stream, pcm_audio_stream_1)

        self.logger.info("Finished loading audio cache.")    

    def _calculate_similarity(self, text1, text2):
        """Calculate the similarity ratio between two strings."""
        return SequenceMatcher(None, text1, text2).ratio()

    def _find_existing_audio(self, text) ->  Tuple[Optional[str], bool]:
        """Check if the audio for the given text already exists in the audio directory."""
        # Check if the text contains any digits
        if any(char.isdigit() for char in text):
            return None, True  # Skip comparison and return None

        for file in os.listdir(self.audio_dir):
            if file.endswith(".mp3"):
                txt_file_path = self.audio_dir / file.replace(".mp3", ".txt")
                if txt_file_path.exists():
                    with open(txt_file_path, "r") as f:
                        saved_text = f.read()
                        if self._calculate_similarity(text, saved_text) >= 0.98:
                            return self.audio_dir / file, False
        return None, False

    def generate_audio(self, text):
        # Process text for replacements
        text = self._process_text(text)
        # Define the path to save the new audio file
        # speech_file_path = self.audio_dir / f"{hash(text)}.mp3"
        # Define the path to save the new audio file using MD5 hash
        text_hash = hashlib.md5(text.encode()).hexdigest()
        speech_file_path = self.audio_dir / f"{text_hash}.mp3"

        existing_audio_path, isdigit = self._find_existing_audio(text)

        # Check if audio is in cache
        if text_hash in self.audio_cache:
            if not isdigit:
                pcm_audio_stream, pcm_audio_stream_1 = self.audio_cache[text_hash]
                # Reset streams to the beginning before returning
                pcm_audio_stream.seek(0)
                pcm_audio_stream_1.seek(0)
                return pcm_audio_stream, pcm_audio_stream_1

        if existing_audio_path:
            # Load existing audio file
            audio_segment = AudioSegment.from_file(existing_audio_path, format="mp3")
        else:
            if not isdigit:
                # Save the corresponding text for future comparison
                with open(speech_file_path.with_suffix('.txt'), "w") as f:
                    f.write(text)

            if self.voice_type == "alloy":
                # Generate speech using OpenAI API
                response = self.client.audio.speech.create(
                    model="tts-1",
                    voice="alloy",
                    input=text
                )

                response.write_to_file(speech_file_path)

                # Load the newly saved audio file
                audio_segment = AudioSegment.from_file(speech_file_path, format="mp3")
            elif self.voice_type == "coqui":
                # url = f"http://localhost:5002/api/tts?text={text}&speaker_id=p263&style_wav=&language_id=en"
                # url = f"http://localhost:5002/api/tts?text={text}&speaker_id={speaker_id}&style_wav=&language_id=en"
                url = f"http://***********:5002/api/tts?text={text}&speaker_id={self.speaker_id}&style_wav=&language_id=en"

                response = requests.get(url)

                # Load the audio file from the response content
                audio_segment = AudioSegment.from_file(io.BytesIO(response.content), format="wav")
                if not isdigit:
                    audio_segment.export(speech_file_path, format="mp3")
            else:
                tts = gTTS(text=text, lang='en', slow=False, tld='co.in')
                if isdigit:
                    # Save speech to a byte stream
                    audio_stream = io.BytesIO()
                    tts.write_to_fp(audio_stream)
                    audio_stream.seek(0)

                    # Convert audio to PCM format
                    audio_segment = AudioSegment.from_file(audio_stream, format="mp3")
                else:
                    tts.save(speech_file_path)

                    # Load the newly saved audio file
                    audio_segment = AudioSegment.from_file(speech_file_path, format="mp3")

        # Convert the audio file to PCM format
        pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)
        pcm_audio_1 = audio_segment.set_frame_rate(16000).set_channels(1).set_sample_width(2)

        # Save PCM audio to a byte stream
        pcm_audio_stream = io.BytesIO()
        pcm_audio.export(pcm_audio_stream, format="raw")
        pcm_audio_stream.seek(0)

        pcm_audio_stream_1 = io.BytesIO()
        pcm_audio_1.export(pcm_audio_stream_1, format="wav")
        pcm_audio_stream_1.seek(0)

        if not isdigit:
            # Cache the generated audio
            self.audio_cache[text_hash] = (pcm_audio_stream, pcm_audio_stream_1)

        return pcm_audio_stream, pcm_audio_stream_1

    def play_audio_stream(self, pcm_audio_stream):
        """Play an audio stream using PyAudio."""
        # Initialize PyAudio
        p = pyaudio.PyAudio()

        # Open a stream
        stream = p.open(format=p.get_format_from_width(2),  # 16-bit audio
                        channels=1,  # Mono
                        rate=44100,  # 44.1 kHz
                        output=True)

        # Read and play audio stream in chunks
        chunk_size = 1024
        pcm_audio_stream.seek(0)
        data = pcm_audio_stream.read(chunk_size)
        while data:
            stream.write(data)
            data = pcm_audio_stream.read(chunk_size)

        # Stop and close the stream
        stream.stop_stream()
        stream.close()
        p.terminate()

    def _process_text(self, text):
        """Process text for replacements."""
        text = text.replace("%", " percent")
        text = text.replace('\n', '\t').strip()
        if time_in_str := re.findall(r'(\d+:\d+\s?(?:AM|PM|am|pm:?))', text):
            for t_12 in time_in_str:
                t_24 = datetime.strftime(datetime.strptime(t_12, "%I:%M %p"), "%H:%M")
                self.logger.info("Converted %s -> %s", t_12, t_24)
                text = text.replace(t_12, t_24)

        if 'IP' in text.split():
            ip_new = '-'.join([i for i in text.split(' ')[-1]]).replace('-.-',
                                                                        ', ')  # *********** -> 1-9-2, 1-6-8, 1, 1
            text = text.replace(text.split(' ')[-1], ip_new).replace(' IP ', ' I.P. ')

        # Raises UnicodeDecodeError within docker container
        text = text.replace("\N{DEGREE SIGN}F", " degrees fahrenheit")
        text = text.replace("\N{DEGREE SIGN}C", " degrees celsius")
        text = ' '.join(text.split())

        return text.strip()


# tts_instance = TextToSpeech(voice_type="google")
tts_instance = TextToSpeech(voice_type="coqui",speaker_id="p364")

# Example usage

"""
if __name__ == "__main__":
    tts = TextToSpeech()
    text = "Hello, this is a test message."
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)

    text = "This is a\nmultiline\nstring"
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)

    text = "This is a\tmultiline\tstring 100"
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)

    text = "This is a\tmultiline\tstring 2000"
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)
"""
