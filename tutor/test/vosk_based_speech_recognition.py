import os
import json
import time
import threading
import queue
import pyaudio
from vosk import Model, KaldiRecognizer
import speech_recognition as sr
from pydub import AudioSegment
import numpy as np

def audio_callback(in_data, frame_count, time_info, status):
    audio_queue.put(in_data)
    return (in_data, pyaudio.paContinue)

def recognize_speech_vosk():
    # Paths to the vosk models in the root project folder
    project_root = os.path.dirname(os.path.dirname(__file__))  # Get the root directory of the project
    model_dirs = [
        os.path.join(project_root, "recognize_models", "vosk-model-en-us-0.22"),
        os.path.join(project_root, "recognize_models", "vosk-model-en-in-0.5")
    ]

    # Ensure the model directories exist
    for model_dir in model_dirs:
        if not os.path.exists(model_dir):
            raise FileNotFoundError(f"Vosk model not found at {model_dir}")

    # Load the Vosk models
    print(f"Loading Vosk models from: {model_dirs}")
    models = [Model(model_dir) for model_dir in model_dirs]
    recognizers = [Kaldi<PERSON><PERSON>ognizer(model, 16000) for model in models]

    # Initialize PyAudio
    p = pyaudio.PyAudio()

    # Create a queue to hold audio data
    global audio_queue
    audio_queue = queue.Queue()

    # Open microphone stream
    stream = p.open(format=pyaudio.paInt16, channels=1, rate=16000, input=True, frames_per_buffer=4096, stream_callback=audio_callback)
    stream.start_stream()

    def save_audio_to_wav(filename, audio_data):
        # Convert bytes to numpy array
        audio_np = np.frombuffer(audio_data, dtype=np.int16)

        # Create an AudioSegment from the numpy array
        audio_segment = AudioSegment(
            data=audio_np.tobytes(),
            sample_width=2,  # 16-bit PCM
            frame_rate=16000,  # Sample rate
            channels=1  # Mono
        )

        # Export the audio segment as a WAV file
        audio_segment.export(filename, format="wav")

    def process_audio():
        last_time = time.time()

        print("Say something!")

        try:
            while True:
                if not audio_queue.empty():
                    data = audio_queue.get()

                    # Save audio data to a file for Google recognition
                    save_audio_to_wav("temp_audio.wav", data)

                    results = []
                    for i, recognizer in enumerate(recognizers):
                        if recognizer.AcceptWaveform(data):
                            result = recognizer.Result()
                            results.append(json.loads(result)["text"])
                        else:
                            results.append("")

                    if all(results):
                        print(f"Model 1 (en-us Large Model): You said: {results[0]}")
                        print(f"Model 2 (en-in Large Model): You said: {results[1]}")

                        # Google Speech Recognition
                        recognizer_google = sr.Recognizer()
                        with sr.AudioFile("temp_audio.wav") as source:
                            audio = recognizer_google.record(source)
                            try:
                                google_result = recognizer_google.recognize_google(audio)
                                print(f"Google Recognizer: You said: {google_result}")
                            except sr.UnknownValueError:
                                print("Google Recognizer could not understand the audio")
                            except sr.RequestError as e:
                                print(f"Could not request results from Google Recognizer; {e}")

                        last_time = time.time()

                    current_time = time.time()
                    # Check if the user paused for more than 3 seconds
                    if current_time - last_time > 3:
                        print("No input detected. Waiting for user to speak...")
        except Exception as e:
            print(f"An error occurred: {e}")
        finally:
            stream.stop_stream()
            stream.close()
            p.terminate()

    # Start the audio processing thread
    audio_thread = threading.Thread(target=process_audio)
    audio_thread.start()

if __name__ == "__main__":
    recognize_speech_vosk()
