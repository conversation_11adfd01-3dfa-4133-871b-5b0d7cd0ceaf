import os
from pathlib import Path
from difflib import SequenceMatcher
import openai
from dotenv import load_dotenv
from pydub import AudioSegment
import io
import pyaudio
from openai import OpenAI

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

client = OpenAI()

def similar(a, b):
    return SequenceMatcher(None, a, b).ratio()


def get_existing_audio(text, audio_dir):
    for file in os.listdir(audio_dir):
        if file.endswith(".mp3"):
            with open(os.path.join(audio_dir, file.replace(".mp3", ".txt")), "r") as f:
                saved_text = f.read()
                if similar(text, saved_text) >= 0.95:
                    return os.path.join(audio_dir, file)
    return None


def text_to_audio(text):
    audio_dir = Path(__file__).parent / "audio"
    audio_dir.mkdir(exist_ok=True)

    existing_audio_path = get_existing_audio(text, audio_dir)

    if existing_audio_path:
        # Load existing audio file
        audio_segment = AudioSegment.from_file(existing_audio_path, format="mp3")
    else:
        # Define the path to save the new audio file
        speech_file_path = audio_dir / f"{hash(text)}.mp3"

        # Generate speech using OpenAI API with a female voice

        """
          response = openai.Audio.create(
            model="tts-1",
            voice="alloy",  # Specify the female voice here, adjust as necessary if specific name is known
            input=text
        )
        """
        response = client.audio.speech.create(
            model="tts-1",
            voice="alloy",
            input=text
        )

        response.write_to_file(speech_file_path)
        # Save the generated speech to an MP3 file
        #with open(speech_file_path, "wb") as f:
        #    f.write(response['audio'])

        # Save the corresponding text for future comparison
        with open(speech_file_path.with_suffix('.txt'), "w") as f:
            f.write(text)

        # Load the newly saved audio file
        audio_segment = AudioSegment.from_file(speech_file_path, format="mp3")

    # Convert the audio file to PCM format
    pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)

    # Save PCM audio to a byte stream
    pcm_audio_stream = io.BytesIO()
    pcm_audio.export(pcm_audio_stream, format="raw")
    pcm_audio_stream.seek(0)

    return pcm_audio_stream


def play_audio_stream(pcm_audio_stream):
    # Initialize PyAudio
    p = pyaudio.PyAudio()

    # Open a stream
    stream = p.open(format=p.get_format_from_width(2),  # 16-bit audio
                    channels=1,  # Mono
                    rate=44100,  # 44.1 kHz
                    output=True)

    # Read and play audio stream in chunks
    chunk_size = 1024
    pcm_audio_stream.seek(0)
    data = pcm_audio_stream.read(chunk_size)
    while data:
        stream.write(data)
        data = pcm_audio_stream.read(chunk_size)

    # Stop and close the stream
    stream.stop_stream()
    stream.close()
    p.terminate()


# Example usage
pcm_audio_stream = text_to_audio("Today is a wonderful day to build something people love!")
play_audio_stream(pcm_audio_stream)
