import os
import pyttsx3
import requests
import speech_recognition as sr
from dotenv import load_dotenv
from langchain import PromptTemplate, <PERSON><PERSON><PERSON><PERSON>
from langchain.llms import OpenAI
from langchain.memory import ConversationBufferMemory
from langchain_openai import ChatOpenAI

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

# Define the list of vehicles
vehicles = ["Car", "Truck", "Motorcycle", "Bicycle"]

# Define the maximum preset amount
MAX_PRESET_AMOUNT = 10000


# Mock function to simulate posting data to a REST API
def post_vehicle_amount(vehicle_number, amount):
    # Replace with actual REST API call logic
    response = requests.post("https://example.com/api/vehicle", json={
        "vehicle_number": vehicle_number,
        "amount": amount
    })
    return response.status_code, response.json()


# Initialize the Language Model (using OpenAI in this example)
#llm = OpenAI(model="text-davinci-003", api_key="your-openai-api-key")
llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0.7)

# Define the prompts
select_vehicle_prompt = PromptTemplate.from_template(
    "Here is a list of vehicles: {vehicles}. Please say the vehicle number."
)
amount_prompt = PromptTemplate.from_template(
    "You selected vehicle number {vehicle_number} which is {vehicle_name}. Please provide the preset amount for this vehicle."
)
invalid_vehicle_prompt = PromptTemplate.from_template(
    "The vehicle number {vehicle_number} is not valid. Please say a valid vehicle number."
)
invalid_amount_prompt = PromptTemplate.from_template(
    "The preset amount {amount} is not valid. It must be less than or equal to {max_amount}. Please provide a valid amount."
)
confirmation_prompt = PromptTemplate.from_template(
    "Your vehicle {vehicle_number} amount is {amount}. Thank you."
)

# Initialize the ConversationBufferMemory
memory = ConversationBufferMemory()

# Create LLMChains for each step
select_vehicle_chain = LLMChain(prompt=select_vehicle_prompt, llm=llm, memory=memory)
amount_chain = LLMChain(prompt=amount_prompt, llm=llm, memory=memory)
invalid_vehicle_chain = LLMChain(prompt=invalid_vehicle_prompt, llm=llm, memory=memory)
invalid_amount_chain = LLMChain(prompt=invalid_amount_prompt, llm=llm, memory=memory)
confirmation_chain = LLMChain(prompt=confirmation_prompt, llm=llm, memory=memory)

# Initialize speech recognition and TTS
recognizer = sr.Recognizer()
tts_engine = pyttsx3.init()


def speak(text):
    tts_engine.say(text)
    tts_engine.runAndWait()


def listen():
    with sr.Microphone() as source:
        print("Listening...")
        audio = recognizer.listen(source)
        try:
            text = recognizer.recognize_google(audio)
            print(f"You said: {text}")
            return text
        except sr.UnknownValueError:
            print("Sorry, I did not understand that.")
            speak("Sorry, I did not understand that. Please try again.")
            return listen()
        except sr.RequestError as e:
            print(f"Could not request results; {e}")
            speak(f"Could not request results; {e}. Please try again.")
            return None


# Function to extract vehicle number using LLM
def extract_vehicle_number(text):
    # Ask LLM to extract vehicle number from the user's text
    result = llm.extract_entities(text, entity_types=["vehicle_number"])
    if result:
        return result[0]["value"]
    return None


# Function to extract amount using LLM
def extract_amount(text):
    # Ask LLM to extract amount from the user's text
    result = llm.extract_entities(text, entity_types=["amount"])
    if result:
        return result[0]["value"]
    return None


def validate_vehicle_number(vehicle_number):
    try:
        vehicle_number = int(vehicle_number)
        if 1 <= vehicle_number <= len(vehicles):
            return True, vehicle_number
        else:
            return False, vehicle_number
    except ValueError:
        return False, vehicle_number


def validate_amount(amount):
    try:
        amount = float(amount)
        if amount <= MAX_PRESET_AMOUNT:
            return True, amount
        else:
            return False, amount
    except ValueError:
        return False, amount


# Function to handle the interaction flow
def handle_interaction():
    # Step 1: Ask the user to select a vehicle
    vehicle_list_str = ", ".join(f"{i + 1}. {v}" for i, v in enumerate(vehicles))

    while True:
        prompt_text = select_vehicle_chain.run(vehicles=vehicle_list_str)
        speak(prompt_text)
        user_input = listen()
        vehicle_number = extract_vehicle_number(user_input)
        if vehicle_number:
            is_valid, vehicle_number = validate_vehicle_number(vehicle_number)
            if is_valid:
                vehicle_name = vehicles[vehicle_number - 1]
                break
            else:
                invalid_prompt_text = invalid_vehicle_chain.run(vehicle_number=vehicle_number)
                speak(invalid_prompt_text)
        else:
            speak("Sorry, I could not detect the vehicle number. Please try again.")

    # Step 2: Ask the user to provide the preset amount for the selected vehicle
    while True:
        amount_prompt_text = amount_chain.run(vehicle_number=vehicle_number, vehicle_name=vehicle_name)
        speak(amount_prompt_text)
        user_input = listen()
        amount = extract_amount(user_input)
        if amount:
            is_valid_amount, amount = validate_amount(amount)
            if is_valid_amount:
                break
            else:
                invalid_amount_prompt_text = invalid_amount_chain.run(amount=amount, max_amount=MAX_PRESET_AMOUNT)
                speak(invalid_amount_prompt_text)
        else:
            speak("Sorry, I could not detect the preset amount. Please try again.")

    # Step 3: Confirm the amount with the user
    confirmation_text = confirmation_chain.run(vehicle_number=vehicle_number, amount=amount)
    speak(confirmation_text)
    print(confirmation_text)

    # Step 4: Post the data to the REST API
    status_code, api_response = post_vehicle_amount(vehicle_number, amount)


# Run the interaction
handle_interaction()
