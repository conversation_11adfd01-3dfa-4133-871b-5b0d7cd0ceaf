import os

import speech_recognition as sr


def audio_to_text(file_path):
    # Initialize the recognizer
    recognizer = sr.Recognizer()

    # Load the audio file
    with sr.AudioFile(file_path) as source:
        audio_data = recognizer.record(source)

    # Recognize speech using Google Speech Recognition
    try:
        text = recognizer.recognize_google(audio_data)
        return text
    except sr.UnknownValueError:
        return "Could not understand audio"
    except sr.RequestError as e:
        return f"Error with the service: {e}"


project_root = os.path.dirname(os.path.dirname(__file__))  # Get the root directory of the project

# Example usage:
file_path = os.path.join(project_root, "audio_sessions", "9920399749", "deacaf70-dc55-4d96-9821-f5376a8f492b",
                         "deacaf70-dc55-4d96-9821-f5376a8f492b.wav")
text = audio_to_text(file_path)
print("Text from audio:", text)
