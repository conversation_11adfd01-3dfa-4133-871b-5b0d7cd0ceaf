import requests
import json


class VehicleService:
    def __init__(self, api_key, token, unique_id, base_url):
        self.headers = {
            'APIKEY': api_key,
            'ACTION': 'Vehicle-Preset',
            'uniqueid': unique_id,
            'utype': 'S',
            'TOKEN': token,
            'Content-Type': 'application/json'
        }
        self.base_url = base_url

    def post_vehicle_data(self, mobile_no, preset_amount, for_userid, org_id, vehicle_no="", preset_qty=-1,
                          fixed_preset="", switch_bypass=True, userid=None, request_time="", lat="", lng="",
                          country_id=10003, zone="Vehicle"):
        payload = {
            "data": json.dumps({
                "MOBILENO": mobile_no,
                "VEHICLENO": vehicle_no,
                "PRESETAMOUNT": preset_amount,
                "PRESETQTY": preset_qty,
                "FIXEDPRESET": fixed_preset,
                "SwitchBypass": switch_bypass,
                "userid": userid or for_userid,
                "RequestTime": request_time,
                "lat": lat,
                "lng": lng,
                "COUNTRYID": country_id,
                "foruserid": for_userid,
                "Org_Id": org_id
            }),
            "zone": zone
        }

        response = requests.post(self.base_url, headers=self.headers, data=json.dumps(payload))
        response.raise_for_status()

        return response.json()


# Example of how to use the service class
if __name__ == "__main__":
    service = VehicleService(
        api_key='VEHICLE100000000001',
        token='160171',
        unique_id='VEHICLE00001',
        base_url='https://ongouat.agsindia.com:8443/fservices/api/reqas/postdata'
    )

    try:
        result = service.post_vehicle_data(
            mobile_no="9595036832",
            preset_amount="240",
            for_userid="160171",
            org_id=8
        )
        print(result)
    except requests.RequestException as e:
        print(f"An error occurred: {e}")
