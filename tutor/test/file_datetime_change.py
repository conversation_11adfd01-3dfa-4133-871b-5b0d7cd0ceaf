import os
import time
import pywintypes
import win32file
import win32con

# Path to the file
file_path = 'web.config'

# Desired modified time in Unix timestamp format
# For example, let's set it to 1st January 2022, 12:00:00
new_time = time.mktime((2022, 11, 18, 9, 11, 20, 12, 0, 0))

# Get the current access time
current_access_time = os.path.getatime(file_path)

# Set the new access and modified times
os.utime(file_path, (current_access_time, new_time))

# To set the creation time, we use pywin32
# Convert the Unix timestamp to a FILETIME structure
file_time = pywintypes.Time(new_time)

# Open the file and set the creation time
handle = win32file.CreateFile(
    file_path,
    win32con.GENERIC_WRITE,
    0,
    None,
    win32con.OPEN_EXISTING,
    win32con.FILE_ATTRIBUTE_NORMAL,
    None
)
win32file.SetFileTime(handle, file_time, None, None)
handle.close()

print(f"Creation, access, and modified times of '{file_path}' have been updated.")
