import re
from fuzzywuzzy import process

from modules.text.word_to_number import words_to_digits


def normalize_vehicle_number(vehicle_number: str) -> str:
    return vehicle_number.replace(" ", "").lower()


def remove_symbols(text):
    # Use regular expression to remove all non-alphanumeric characters
    cleaned_text = re.sub(r'[^a-zA-Z0-9]', '', text)
    return cleaned_text


def extract_numbers(text):
    # Use regular expression to find all sequences of digits in the text
    numbers = re.findall(r'\d+', text)
    return numbers


def extract_vehicle_number(text: str, vehicle_number_list) -> str | None:
    if len(vehicle_number_list) == 0:
        return None

    normalized_text = remove_symbols(text.lower())
    normalized_text_remove_space = normalize_vehicle_number(normalized_text)
    numbers = extract_numbers(normalized_text_remove_space)
    vehicles = []

    for number in numbers:
        for vehicle in vehicle_number_list:
            if number in vehicle:
                vehicles.append(vehicle)

    if len(vehicles) == 1:
        return vehicles[0]

    vehicle_number = None
    best_match, ratio = process.extractOne(normalized_text_remove_space, vehicle_number_list)
    if ratio >= 46:
        vehicle_number = best_match

    return vehicle_number if vehicle_number in vehicle_number_list else None


test_cases = [
    "okay56",
    "Okay DS T 897 And Preset amount is 100"
]

vehicle_number_list = ['ka5656', 'ab1234', 'bc4576', 'dhdh897']

for sentence in test_cases:
    phrase = sentence.lower()
    phrase = words_to_digits(phrase)
    print(f"Original phrase: {sentence}")
    print(f"Converted phrase: {phrase}")
    vehicle_number = extract_vehicle_number(phrase, vehicle_number_list)
    print(f"Extracted vehicle number: {vehicle_number}")
    print("=====")
