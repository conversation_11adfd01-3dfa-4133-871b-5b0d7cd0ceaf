import speech_recognition as sr
import time

# Initialize recognizer
recognizer = sr.Recognizer()

# Function to detect speech and convert to text using PocketSphinx
def recognize_speech(recognizer, audio):
    try:
        text = recognizer.recognize_sphinx(audio)
        print("recognize_sphinx text: " + text)
        text = recognizer.recognize_google(audio)
        print("recognize_google text: " + text)
    except sr.UnknownValueError:
        print("PocketSphinx could not understand audio")
    except sr.RequestError as e:
        print(f"Could not request results from PocketSphinx service; {e}")

def main():
    # Use the microphone as the source for input.
    with sr.Microphone() as source:
        print("Say something!")
        recognizer.adjust_for_ambient_noise(source)  # Adjust for ambient noise

        last_time = time.time()

        while True:
            try:
                audio = recognizer.listen(source, timeout=5, phrase_time_limit=10)
                recognize_speech(recognizer, audio)

                current_time = time.time()
                # Check if the user paused for more than 3 seconds
                if current_time - last_time > 3:
                    break
                last_time = current_time
            except sr.WaitTimeoutError:
                print("Listening timed out while waiting for phrase to start")
                break

if __name__ == "__main__":
    main()
