import os
import pyttsx3
import requests
import speech_recognition as sr
from dotenv import load_dotenv
from langchain import PromptTemplate, <PERSON><PERSON><PERSON><PERSON>
from langchain.llms import OpenAI
from langchain.memory import ConversationBufferMemory
from langchain_openai import ChatOpenAI
from langchain.chains import ConversationChain

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

# Define the list of vehicle numbers
vehicle_numbers = ["AB1234", "BC1234", "AC1234"]

# Define the maximum preset amount
MAX_PRESET_AMOUNT = 10000


# Mock function to simulate posting data to a REST API
def post_vehicle_amount(vehicle_number, amount):
    response = requests.post("https://example.com/api/vehicle", json={
        "vehicle_number": vehicle_number,
        "amount": amount
    })
    return response.status_code, response.json()


conversation_memory = ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant",
                                               initial_input={"vehicle_numbers": vehicle_numbers})

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0.7)
greeting = "Thank you for calling Ongo Ags. My name is <PERSON>, how may I assist you?"
PROMPT_TEMPLATE = """
    You are Sandy, an AI Assistant for Ongo Ags. 
    Ask me: "Thank you for calling Ongo Ags. My name is Sandy, could you please provide the vehicle number and 
    the amount you wish to preset now?"
    If the user does not provide the amount in the first response, ask: "Thank you for providing the vehicle number. 
    How much would you like to preset now?"
    If the vehicle number does not match the list of vehicles, ask: "Could you please provide a valid vehicle number 
    and the amount you wish to preset now?"
    If the vehicle number and amount value is got it, ask: "i place your preset amount request"    

    Only ask one question at a time. Do ask follow-up questions if you think it's necessary.
    Here is a list of vehicles: {vehicle_numbers}
    Current Conversation:
    {history}
    User: {input}
    Assistant:"""

PROMPT_TEMPLATE = PROMPT_TEMPLATE.replace("{vehicle_numbers}", ", ".join(vehicle_numbers))
# Improved prompt template for the AI
PROMPT = PromptTemplate(input_variables=["history", "input"], template=PROMPT_TEMPLATE)

conversation_chain = ConversationChain(prompt=PROMPT, llm=llm, memory=conversation_memory)

context = {
    "vehicle_numbers": vehicle_numbers,
    # Other context variables if needed
}


def answer_call_back(human_input: str):
    if not human_input:
        input_text = "Sorry, I didn't get that. Please try again."
        # conversation_history.append(Message("ai", input_text))
        return input_text
    else:
        input_text = human_input

    ai_response = conversation_chain.run(input_text)

    return ai_response


# Define the prompts
select_vehicle_prompt = PromptTemplate.from_template(
    "Here is a list of vehicles: {vehicle_numbers}. Please say the vehicle number."
)

amount_prompt = PromptTemplate.from_template(
    "You selected vehicle number {vehicle_number}. Please provide the preset amount for this vehicle."
)

invalid_vehicle_prompt = PromptTemplate.from_template(
    """
    Prompt:
    User Input: {user_input}
    List of Vehicles: {vehicle_numbers}
    
    Task: Determine if the user input matches any vehicle number from the provided list.
    
    Instructions:
    1. If a vehicle number from the list is not found in the user input, return "Output: no".
    2. If a vehicle number from the list is found in the user input, return "Output: yes".
    
    Output Format:
    "Output: yes" or "Output: no"

    """
    )

invalid_amount_prompt = PromptTemplate.from_template(
    """
    Prompt: 
    User Input: {user_input}
    Validator Question: {validator_response}
    
    Task: Determine if the user input corresponds to an amount that must be less than or equal to {max_amount}.
    
    Instructions:
    1. Extract the numerical amount from the user input.
    2. Compare this amount to the specified max amount ({max_amount}).
    3. If the validator question is related to "Could you please provide the amount you wish to preset now?" question, return "Output: no".
    4. Otherwise, return "Output: yes" if the extracted amount is less than or equal to the max amount; return "Output: no" if it is greater.
    
    Output Format:
    "Output: yes" or "Output: no"
    """
)

confirmation_prompt = PromptTemplate.from_template(
    "Your vehicle {vehicle_number} amount is {amount}. Thank you."
)

# Initialize the ConversationBufferMemory
memory = ConversationBufferMemory()

# Create LLMChains for each step
select_vehicle_chain = LLMChain(prompt=select_vehicle_prompt, llm=llm, memory=memory)
amount_chain = LLMChain(prompt=amount_prompt, llm=llm, memory=memory)

invalid_vehicle_chain = LLMChain(prompt=invalid_vehicle_prompt, llm=llm)

invalid_amount_chain = LLMChain(prompt=invalid_amount_prompt, llm=llm)

confirmation_chain = LLMChain(prompt=confirmation_prompt, llm=llm, memory=memory)

# Initialize speech recognition and TTS
recognizer = sr.Recognizer()
tts_engine = pyttsx3.init()


def speak(text):
    tts_engine.say(text)
    tts_engine.runAndWait()


def listen():
    with sr.Microphone() as source:
        print("Listening...")
        audio = recognizer.listen(source)
        try:
            text = recognizer.recognize_google(audio)
            print(f"You said: {text}")
            return text
        except sr.UnknownValueError:
            print("Sorry, I did not understand that.")
            speak("Sorry, I did not understand that. Please try again.")
            return listen()
        except sr.RequestError as e:
            print(f"Could not request results; {e}")
            speak(f"Could not request results; {e}. Please try again.")
            return None


def validate_vehicle_number(vehicle_number):
    if vehicle_number in vehicle_numbers:
        return True, vehicle_number
    else:
        return False, vehicle_number


def validate_amount(amount):
    try:
        amount = float(amount)
        if amount <= MAX_PRESET_AMOUNT:
            return True, amount
        else:
            return False, amount
    except ValueError:
        return False, amount


# Function to handle the interaction flow
def handle_interaction():
    # Step 1: Ask the user to select a vehicle
    vehicle_list_str = ", ".join(vehicle_numbers)
    prompt_text = answer_call_back("ask me first question")
    speak(prompt_text)
    user_first_input = ""

    while True:
        # prompt_text = select_vehicle_chain.run(vehicle_numbers=vehicle_list_str)
        user_input = listen()
        user_first_input = user_input
        ai_response = answer_call_back(user_input)
        print(f"ai said: {ai_response}")
        is_valid = invalid_vehicle_chain.run(vehicle_numbers=vehicle_list_str, user_input=user_input)
        is_valid, vehicle_number = validate_vehicle_number(user_input)
        if ai_response.startswith("Could you please provide a valid vehicle number"):
            # invalid_prompt_text = invalid_vehicle_chain.run(vehicle_number=vehicle_number)
            speak(ai_response)
        else:
            break

    # Step 2: Ask the user to provide the preset amount for the selected vehicle
    while True:
        # Define the phrase you want to check if it is within the AI response
        phrase = "How much would you like to preset now?"

        # Check if the phrase is within the AI response
        if phrase in ai_response:
            user_input = listen()
            ai_response = answer_call_back(user_input)

        invalid_amount_prompt_text = invalid_amount_chain.run(user_input=user_input, validator_response=ai_response, max_amount=MAX_PRESET_AMOUNT)
        print(f"invalid_amount_chain: {invalid_amount_prompt_text}")

        amount_prompt_text = amount_chain.run(vehicle_number=ai_response)
        speak(amount_prompt_text)
        user_input = listen()
        is_valid_amount, amount = validate_amount(user_input)
        if is_valid_amount:
            break
        else:
            invalid_amount_prompt_text = invalid_amount_chain.run(amount=user_input, max_amount=MAX_PRESET_AMOUNT)
            speak(invalid_amount_prompt_text)

    # Step 3: Confirm the amount with the user
    confirmation_text = confirmation_chain.run(vehicle_number=ai_response, amount=amount)
    speak(confirmation_text)
    print(confirmation_text)

    # Step 4: Post the data to the REST API
    status_code, api_response = post_vehicle_amount(vehicle_number, amount)
    if status_code == 200:
        speak("Data has been successfully sent.")
    else:
        speak("There was an error sending the data.")


# Run the interaction
handle_interaction()
