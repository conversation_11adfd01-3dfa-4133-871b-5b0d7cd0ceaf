import io

import pyaudio
import requests
from pydub import AudioSegment


def text_to_audio_stream(text):
    url = f"http://localhost:5002/api/tts?text={text}&speaker_id=p263&style_wav=&language_id=en"

    response = requests.get(url)

    # Load the audio file from the response content
    audio_segment = AudioSegment.from_file(io.BytesIO(response.content), format="wav")

    # Convert the audio file to PCM format
    pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)

    # Save PCM audio to a byte stream
    pcm_audio_stream = io.BytesIO()
    pcm_audio.export(pcm_audio_stream, format="raw")
    pcm_audio_stream.seek(0)

    return pcm_audio_stream


def play_audio_stream(pcm_audio_stream):
    # Initialize PyAudio
    p = pyaudio.PyAudio()

    # Open a stream
    stream = p.open(format=p.get_format_from_width(2),  # 16-bit audio
                    channels=1,  # Mono
                    rate=44100,  # 44.1 kHz
                    output=True)

    # Read and play audio stream in chunks
    chunk_size = 1024
    pcm_audio_stream.seek(0)
    data = pcm_audio_stream.read(chunk_size)
    while data:
        stream.write(data)
        data = pcm_audio_stream.read(chunk_size)

    # Stop and close the stream
    stream.stop_stream()
    stream.close()
    p.terminate()


# Example usage
text = "These cards can be ordered online thru Ongo app, https://ongo.co.in/ and Ongo card can be bought at Ongo Authorized Agent network outlets."
pcm_audio_stream = text_to_audio_stream(text)
play_audio_stream(pcm_audio_stream)
