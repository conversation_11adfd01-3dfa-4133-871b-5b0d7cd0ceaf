import unittest

from modules.audio.text_speech import TextToSpeech


class TestTextToSpeech(unittest.TestCase):
    def setUp(self):
        # Initialize TextToSpeech object
        self.tts = TextToSpeech()

    def test_process_text_time_conversion(self):
        # Test time conversion
        input_text = "The meeting is at 3:30 PM"
        expected_output = "The meeting is at 15:30"
        processed_text = self.tts.process_text(input_text)
        self.assertEqual(processed_text, expected_output)

    def test_process_text_ip_formatting(self):
        # Test IP formatting
        input_text = "The IP address is ***********"
        expected_output = "The I.P. address is 1-9-2, 1-6-8, 1, 1"
        processed_text = self.tts.process_text(input_text)
        self.assertEqual(processed_text, expected_output)

    def test_process_text_degree_conversion(self):
        # Test degree symbol conversion
        input_text = "The temperature is 25°C"
        expected_output = "The temperature is 25 degrees celsius"
        processed_text = self.tts.process_text(input_text)
        self.assertEqual(processed_text, expected_output)

    def test_process_text_newline_replacement(self):
        # Test newline replacement
        input_text = "This is a\nmultiline\nstring"
        expected_output = "This is a\tmultiline\tstring"
        processed_text = self.tts.process_text(input_text)
        self.assertEqual(processed_text, expected_output)


if __name__ == '__main__':
  unittest.main()