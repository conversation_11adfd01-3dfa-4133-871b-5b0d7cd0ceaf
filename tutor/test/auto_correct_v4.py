import re
import difflib

def replace_with_correct_word(user_input, correct_word, match_threshold=0.4):
    user_input = user_input.lower()
    correct_word = correct_word.lower()
    # Refine the pattern to better match vehicle number formats
    pattern = r'\b(?:[a-z]{1,2}\s*\d{1,4}[a-z]{0,2}|\d{4,8}[a-z]{0,2}|\d{2,4}[a-z]{1,2}\d{1,4})\b'

    # Search for the pattern in the user input
    matches = re.findall(pattern, user_input)

    # Only replace if matches are found and match ratio exceeds the threshold
    if matches:
        for match in matches:
            match_ratio = difflib.SequenceMatcher(None, match, correct_word).ratio()
            if match_ratio >= match_threshold:
                user_input = user_input.replace(match, correct_word)

    return user_input

# Example usage
correct_word = "mp041234th"

input_sentences = [
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
    "Vehicle A 12 with precise amount of 100.",
    "Car number AB1234 is parked outside.",
    "The registration is AB1234.",
    "See the plate: AB  1234.",
    "He mentioned A123.",
    "You noted 12 AB right?",
    "The number is 1A2B3.",
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
    "Vehicle A 12 with precise amount of 100.",
    "Some more examples MH 09 F 787878 and MP 04 1234 TH should be matched.",
    "Random inputs like KA 51 MC 4084 and VSGGDHDH 897 need to be replaced.",
    "Mixed patterns such as 23 BH 8890 A and AB 1234 are also included."
]

for input_sentence in input_sentences:
    corrected_sentence = replace_with_correct_word(input_sentence, correct_word)
    print(corrected_sentence)

