import re


def format_numbers_with_spaces(text):
    # Define a regular expression pattern for detecting phone and customer numbers, including those with hyphens
    pattern = r'\b\d{3,4}[-]?\d{3}[-]?\d{4,7}\b'

    def replace_with_spaces(match):
        # Get the matched number
        number = match.group()
        # Remove any hyphens from the number
        number = number.replace('-', '')
        # Insert spaces between each digit
        spaced_number = ' '.join(number)
        return spaced_number

    # Substitute the numbers in the text with the spaced versions
    edited_text = re.sub(pattern, replace_with_spaces, text)

    return edited_text


# Test examples
text1 = "my number is 7038360800"
text2 = "customer number is 180000001515"
text3 = "call me at 1800-267-6646"
text4 = "Cash loading to PPI shall be limited to Rs. 10,000/-"

edited_text1 = format_numbers_with_spaces(text1)
edited_text2 = format_numbers_with_spaces(text2)
edited_text3 = format_numbers_with_spaces(text3)
edited_text4 = format_numbers_with_spaces(text4)

print(edited_text1)  # Output: my number is 7 0 3 8 3 6 0 8 0 0
print(edited_text2)  # Output: customer number is 1 8 0 0 0 0 0 0 1 5 1 5
print(edited_text3)  # Output: call me at 1 8 0 0 2 6 7 6 6 4 6
print(edited_text4)  # Output: Cash loading to PPI shall be limited to Rs. 10,000/-
