class VehicleHandler:
    def __init__(self):
        self.vehicle_top_make_list = {'toyota', 'mahindra', 'maruti', 'ford', 'honda', 'bmw', 'audi',
                                      'mercedes-benz', 'chevrolet', 'nissan', 'hyundai', 'kia',
                                      'volkswagen', 'subaru', 'mazda', 'jeep', 'dodge', 'lexus',
                                      'infiniti', 'acura'}
        self.vehicle_make_list = {'bmw'}  # Add more makes as needed
        self.vehicle_numbers = {'ab1234', 'bmw12345'}
        self.vehicle_data = {
            'bmw': ['bmw12345', 'bmw67890'],  # Example data, add more as needed
            # Add other makes with their respective vehicle numbers
        }

    def _not_tagged_vehicle_make_response(self, vehicle_make: str) -> str:
        """Generates a response for a not tagged vehicle make."""
        return (f"{vehicle_make} is not tagged. Is your vehicle make one of the following: "
                f"{', '.join(self.vehicle_top_make_list)}?")

    def _store_vehicle_number(self, vehicle_number: str):
        """Stores the vehicle number for future reference."""
        # Implement the logic to store the vehicle number
        print(f"Storing vehicle number: {vehicle_number}")

    def _handle_multiple_vehicle_numbers(self, vehicle_make: str) -> str | None:
        """Handles cases where multiple vehicle numbers are associated with a make."""
        vehicle_numbers = self.vehicle_data.get(vehicle_make, [])
        if len(vehicle_numbers) == 1:
            self._store_vehicle_number(vehicle_numbers[0])
        elif len(vehicle_numbers) > 1:
            return (f"You have the vehicles {', '.join(vehicle_numbers)} with the make {vehicle_make}. "
                    "Do you want to select a specific vehicle or all vehicles?")
        return None

    def _handle_vehicle_make(self, phrase: str) -> str | None:
        """Handles vehicle make logic and returns appropriate response if necessary."""
        words = phrase.split()
        for word in words:
            if word in self.vehicle_top_make_list and word not in self.vehicle_make_list and not any(
                    word in vn for vn in self.vehicle_numbers):
                return self._not_tagged_vehicle_make_response(word)
            elif word in self.vehicle_make_list:
                response = self._handle_multiple_vehicle_numbers(word)
                if response:
                    return response

        return None


# Example usage
handler = VehicleHandler()
sentences = [
    'my make is bmw',
    'I drive a toyota',
    'I own a honda vehicle',
    'The car is a ford',
    'I have a nissan',
    'mercedes-benz is my vehicle make',
    'I use a kia',
    'My vehicle make is hyundai',
    'lexus is the make of my car',
    'subaru is what I drive',
]

for sentence in sentences:
    response = handler._handle_vehicle_make(sentence)
    if response:
        print(response)
