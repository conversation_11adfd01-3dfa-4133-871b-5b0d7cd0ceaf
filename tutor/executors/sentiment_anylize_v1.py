import json
import os
import string
from datetime import datetime

import requests

from tutor.modules.assistants.sentiment_analyze_assistant import SentimentAnalyzeAssistant
from tutor.modules.database import database
from tutor.modules.database.msql_database import Database
from tutor.modules.logger import logger, multiprocessing_logger
from tutor.modules.models import models
from tutor.modules.utils import shared


def anylize() -> None:
    multiprocessing_logger(filename=os.path.join('logs', 'background_tasks_%d-%m-%Y.log'))
    try:
        # db = Database(database="sampledb", server="***********,1433", user="mayur", password="123456")
        db = database.Database(database=models.fileio.base_db)
        unanalyzed_records = db.get_unanalyzed_records("Complaints")

        for record in unanalyzed_records:
            sentiment_assistant = SentimentAnalyzeAssistant(logger=logger, record=record, db=db)
            text_sentiment, sentiment, score, emotions = sentiment_assistant.analyze_sentiment()
            db.update_record_with_sentiment(record["complaint_id"], sentiment, score, emotions, text_sentiment)
            print(f"Processed record {record['complaint_id']} with sentiment: {sentiment}, score: {score}, emotions: {emotions}")
    except TypeError:
        logger.error("Failed anylize")
        return
    
def anylize_post() -> None:
    multiprocessing_logger(filename=os.path.join('logs', 'background_tasks_%d-%m-%Y.log'))
    try:
        # db = Database(database="sampledb", server="***********,1433", user="mayur", password="123456")
        db = database.Database(database=models.fileio.base_db)

        # Authentication and Sentiment Analysis URLs
        auth_url = "https://***********:5012/api/auth/login"
        create_url = "https://***********:5012/api/SentimentAnalysisResult/create"


        # Production
        # auth_url = "http://************:44312/api/auth/login"
        # create_url = "http://************:44312/api/SentimentAnalysisResult/create"

        # Authentication payload and headers
        auth_payload = {
            "email": "<EMAIL>",
            "Password": "Admin@123"
        }

        token  = shared.token
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}' if token else '',
        }

        # Initialize and run
        sentiment_poster = SentimentPoster(db=db, auth_url=auth_url, create_url=create_url, auth_payload=auth_payload, headers=headers)
        sentiment_poster.anylize_post()
    except TypeError:
        logger.error("Failed anylize")
        return    
    

class SentimentPoster:
    def __init__(self, db: Database, auth_url: str, create_url: str, auth_payload: dict, headers: dict):
        self.db = db
        self.auth_url = auth_url
        self.create_url = create_url
        self.auth_payload = auth_payload
        self.headers = headers
        self.token = None

    def authenticate(self) -> bool:
        """Fetches a new authentication token and updates headers."""
        try:
            response = requests.post(self.auth_url, headers={'Content-Type': 'application/json'}, data=json.dumps(self.auth_payload), verify=False)
            if response.status_code == 200:
                self.token = response.json().get("Token")
                if self.token:
                    self.headers['Authorization'] = f"Bearer {self.token}"
                    shared.token = self.token
                    logger.info("Successfully authenticated and retrieved token.")
                    return True
                else:
                    logger.error("Failed to retrieve access token.")
                    return False
            else:
                logger.error(f"Authentication failed with status code {response.status_code}: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return False

    def post_sentiment_data(self, record: dict) -> bool:
        """Posts sentiment data to the create API and handles token expiration."""
        payload = {
            "ComplaintId": record["complaint_id"],
            "AccountId": record["account_id"],
            "CustomerName": record["customer_name"],
            "Subject": record["subject"],
            "Description": record["description"],
            "Status": record["status"],
            "CreatedOnUtc": record["date"],
            "IsSentimentAnalyzed": record["is_sentiment_analyzed"],
            "SentimentCategory": record.get("sentiment_category", "Neutral"),
            "SentimentScore": record.get("sentiment_score", 0),
            "KeyEmotions": record.get("key_emotions", "Neutral"),
            "TextSentiment": record.get("text_sentiment", "")
        }

        try:
            response = requests.post(self.create_url, headers=self.headers, data=json.dumps(payload), verify=False)
            if response.status_code == 200:
                self.db.update_record_with_is_posted(record["complaint_id"])
                logger.info(f"Successfully posted record {record['complaint_id']} with sentiment data.")
                print(f"Posted record {record['complaint_id']} with sentiment data.")
                return True
            elif response.status_code == 401:
                logger.warning("Token expired. Re-authenticating...")
                if self.authenticate():  # Retry authentication
                    # Retry posting with new token
                    response = requests.post(self.create_url, headers=self.headers, data=json.dumps(payload), verify=False)
                    if response.status_code == 200:
                        logger.info(f"Successfully posted record {record['complaint_id']} with sentiment data after re-authentication.")
                        self.db.update_record_with_is_posted(record["complaint_id"])
                        print(f"Posted record {record['complaint_id']} with sentiment data after re-authentication.")
                        return True
                    else:
                        logger.error(f"Failed to post record {record['complaint_id']} after re-authentication: {response.text}")
                else:
                    logger.error("Re-authentication failed. Cannot post sentiment data.")
            else:
                logger.error(f"Failed to post record {record['complaint_id']}: {response.text}")
        except Exception as e:
            logger.error(f"Error posting sentiment data for record {record['complaint_id']}: {e}")
        return False

    def anylize_post(self) -> None:
        """Processes unanalyzed records and posts them to the Sentiment Analysis Result API."""
        try:
            unanalyzed_records = self.db.get_unposted_records("Complaints")

            for record in unanalyzed_records:
                self.post_sentiment_data(record)
        except Exception as e:
            logger.error(f"Error in anylize_post: {e}")
            print(f"Error in anylize_post: {e}")    