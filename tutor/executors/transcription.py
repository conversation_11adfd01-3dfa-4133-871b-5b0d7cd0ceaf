import os
import time
import asyncio
import glob
from pathlib import Path
from typing import Dict, List, Optional

from tutor.modules.logger import logger
from tutor.modules.models import models
from tutor.modules.transcription.hinglish_transcriber import HinglishTranscriber
from tutor.modules.database import database

class AudioProcessor:
    """Simple audio file processor for transcription."""
    
    def __init__(self):
        self.logger = logger
        self.db = database.Database(database=models.fileio.base_db)
        self.transcriber = HinglishTranscriber(logger)
        self.processing_queue = asyncio.Queue()
        self.processed_files = set()
        
    def _extract_metadata_from_path(self, file_path: str) -> Dict:
        """Extract call metadata from file path."""
        path = Path(file_path)
        # Expected path format: sessions/{mobile}/{session_id}/call_recording.wav
        parts = path.parts
        
        # Default values
        metadata = {
            "call_id": str(int(time.time())),
            "timestamp": time.time()
        }
        
        # Try to extract mobile and session_id from path
        try:
            sessions_index = parts.index("audio_storage")
            if len(parts) > sessions_index + 2:
                metadata["mobile"] = parts[sessions_index + 1]
                metadata["call_id"] = parts[sessions_index + 2]
        except (ValueError, IndexError):
            self.logger.warning(f"Could not extract metadata from path: {file_path}")
            
        return metadata
    
    async def scan_directory(self):
        """Scan directory for new audio files."""
        sessions_dir = models.fileio.audio_storage
        self.logger.info(f"Scanning directory for audio files: {sessions_dir}")
        
        while True:
            # Find all audio files
            audio_files = []
            for ext in ['.wav', '.mp3']:
                audio_files.extend(glob.glob(f"{sessions_dir}/**/*{ext}", recursive=True))
            
            # Process new files
            for file_path in audio_files:
                if file_path not in self.processed_files:
                    metadata = self._extract_metadata_from_path(file_path)
                    
                    # Check if this file has already been processed in the database
                    transcript = self.db.get_transcript(metadata["call_id"])
                    if transcript and transcript.get("is_audio_processed") == 1:
                        self.logger.info(f"Audio file already processed according to database: {file_path}")
                        self.processed_files.add(file_path)
                        continue
                    
                    self.logger.info(f"New audio file found: {file_path}")
                    await self.processing_queue.put((file_path, metadata))
                    self.processed_files.add(file_path)
            
            # Wait before next scan
            await asyncio.sleep(5)
    
    async def process_queue(self):
        """Process audio files in the queue."""
        self.logger.info("Starting audio processing queue")
        while True:
            try:
                file_path, metadata = await self.processing_queue.get()
                self.logger.info(f"Processing audio file: {file_path}")
                call_id = metadata["call_id"]
                
                # Check if already processed
                transcript = self.db.get_transcript(call_id)
                if transcript and transcript.get("is_audio_processed") == 1:
                    self.logger.info(f"Transcript already exists for call_id: {call_id}")
                    self.processing_queue.task_done()
                    continue
                
                # Process the file
                result = await self.transcriber.transcribe_audio(file_path)
                
                if "error" in result:
                    self.logger.error(f"Transcription error: {result['error']}")
                    # Mark as processed even if there's an error to prevent reprocessing
                    self.processed_files.add(file_path)
                else:
                    # Save to database
                    await self.transcriber.save_to_database(
                        result, call_id, self.db
                    )
                    self.logger.info(f"Successfully transcribed and saved: {file_path}")
                    
                    # Ensure the transcript is properly marked as not yet QA processed
                    # This is the key fix - ensure is_qa_processed is explicitly set to 0
                    self.db.ensure_transcript_ready_for_qa(call_id, file_path)
                
                self.processing_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"Error processing audio file: {str(e)}")
                self.processing_queue.task_done()
                
            # Small delay to prevent CPU overuse
            await asyncio.sleep(0.1)

def transcription_service():
    """Background service for audio transcription."""
    logger.info("Starting transcription service")
    
    # Set up the audio processor
    processor = AudioProcessor()
    
    # Set up the asyncio event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Start the directory scanner and processing queue
        loop.create_task(processor.scan_directory())
        loop.create_task(processor.process_queue())
        loop.run_forever()
    except KeyboardInterrupt:
        logger.info("Transcription service interrupted")
    except Exception as e:
        logger.error(f"Error in transcription service: {str(e)}")
    finally:
        logger.info("Transcription service stopped")
