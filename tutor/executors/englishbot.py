import importlib
import logging
import os
import time
from urllib.parse import urljoin

from tutor.executors import process_map
from tutor.modules.exceptions import (BotInUse, BotWebhookConflict,
                                       EgressErrors)
from tutor.modules.firebase import configuration
from tutor.modules.logger import logger, multiprocessing_logger
from tutor.modules.models import models
from tutor.modules.englishbot import bot

importlib.reload(module=logging)

FAILED_CONNECTIONS = {'count': 0}


def get_webhook_origin(retry: int) -> str:
    """Get the telegram bot webhook origin.

    Args:
        retry: Number of retry attempts to get public URL.

    Returns:
        str:
        Public URL where the englishbot webhook is hosted.
    """
    if models.env.english_bot_webhook:
        return str(models.env.english_bot_webhook)


def englishbot_api(webhook_trials: int = 20) -> None:
    """Initiates polling for new messages.

    Args:
        webhook_trials: Number of retry attempts to get the public URL for <PERSON> (if hosted via <PERSON><PERSON>)

    See Also:
        - ``webhook_trials`` is set to 3 when polling fails (which is already a fallback for webhook retries)

    Handles:
        - BotWebhookConflict: When there's a broken webhook set already.
        - BotInUse: Restarts polling to take control over.
        - EgressErrors: Initiates after 10, 20 or 30 seconds. Depends on retry count. Restarts after 3 attempts.
    """
    multiprocessing_logger(filename=os.path.join('logs', 'englishbot_api_%d-%m-%Y.log'))
    if not models.env.english_bot_token:
        logger.info("Bot token is required to start the english Bot")
        return
    if (public_url := get_webhook_origin(webhook_trials)) and (response := configuration.set_url(
            base_url=public_url, webhook=urljoin(public_url, models.env.english_bot_endpoint), logger=logger)
    ):
        logger.info("EnglishBot API will be hosted via webhook.")
        logger.info(response)
        process_map.remove(englishbot_api.__name__)
        return
    try:
        # bot.poll_for_messages()
        logger.info("EnglishBot API will be hosted via webhook. poll_for_messages")
    except BotWebhookConflict as error:
        # At this point, its be safe to remove the dead webhook
        logger.error(error)
        configuration.delete_url(base_url=bot.BASE_URL, logger=logger)
        englishbot_api(3)
    except BotInUse as error:
        logger.error(error)
        logger.info("Restarting message poll to take over..")
        englishbot_api(3)
    except EgressErrors as error:
        logger.error(error)
        FAILED_CONNECTIONS['count'] += 1
        if FAILED_CONNECTIONS['count'] > 3:
            logger.critical("ATTENTION::Couldn't recover from connection error. Restarting current process.")
            # controls.restart_control(quiet=True)
        else:
            logger.info("Restarting in %d seconds.", FAILED_CONNECTIONS['count'] * 10)
            time.sleep(FAILED_CONNECTIONS['count'] * 10)
            englishbot_api(3)
    except Exception as error:
        logger.critical("ATTENTION: %s", error.__str__())
        # controls.restart_control(quiet=True)
