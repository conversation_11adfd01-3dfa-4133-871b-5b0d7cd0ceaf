import random
from datetime import datetime

from tutor.modules.models import models
from tutor.modules.utils import util


def form(*args) -> str:
    """Response for form."""
    return "I am a program, I'm without form."


def greeting(*args) -> str:
    """Response for greeting."""
    return random.choice(['Hi', 'Hello'])


def capabilities(*args) -> str:
    """Response for capabilities."""
    return ('There is a lot I can do. For example: I can get you the weather at any location, news around '
            'you, meanings of words, launch applications, create a to-do list, check your emails, get your '
            'system configuration, tell your investment details, locate your phone, find distance between '
            'places, set an alarm, play music on smart devices around you, control your TV, tell a joke, '
            'send a message, set reminders, scan and clone your GitHub repositories, and much more. '
            'Time to ask,.')


def languages(*args) -> str:
    """Response for languages."""
    return "Tricky question!. I'm configured in python, and I can speak English."


def whats_up(*args) -> str:
    """Response for what's up."""
    return "My listeners are up. There is nothing I cannot process. So ask me anything.."


def what(*args) -> str:
    """Response for what."""
    return "The name is <PERSON>. I'm just a pre-programmed virtual assistant."


def hi(*args) -> str:
    """Response for hi and hello."""
    return "Hello there!" + random.choice((f", good {util.part_of_day()}! How can I be of service today?",
                                           ", and I'm ready to assist you. How can I help you today?"))


def who(*args) -> str:
    """Response for whom."""
    return "I am Jarvis. A virtual assistant designed by Mr. Mayur."


def about_me(*args) -> str:
    """Response for about me."""
    return ("I am a virtual assistant designed by Mr. Mayur. "
            "Given enough access I can be your home assistant. "
            "I can seamlessly take care of your daily tasks, and also help with most of your work!")


def not_allowed_offline() -> str:
    """Response for tasks not supported via offline communicator."""
    return "That's not supported via offline communicator."


def un_processable(title) -> str:
    """Speaker response for un-processable requests."""
    return f"I'm sorry {title}! I wasn't able to process your request."


def sending_user_request() -> str:
    responses = [
        "We are working on it, please wait.",
        "Your request is in progress.",
        "Hold on, we are on it.",
        "Almost ready.",
        "Please stand by.",
        "Just a moment.",
        "We are taking care of it.",
        "Please wait.",
        "We are on it.",
    ]
    return random.choice(responses)


def sending_user_request_1() -> str:
    responses = [
        "We are working on your request right now, please wait.",
        "Your request is being processed, thank you for your patience.",
        "Please hold on, we're handling your request.",
        "We're on it! Your request will be ready shortly.",
        "Processing your request, please stand by."
    ]
    return random.choice(responses)


def get_success_message(amount: str, vehicle_number: str) -> str:
    amount = str(amount).lower()
    vehicle_number = str(vehicle_number).lower()

    if amount == 'full':
        if vehicle_number == 'all':
            messages = [
                "All vehicles are now full.",
                "All your vehicles are set to full.",
                "Full amount applied to all vehicles.",
                "Every vehicle is now full.",
                "Full amount set for all vehicles."
            ]
        else:
            messages = [
                f"Vehicle {vehicle_number} is now full.",
                f"Full amount applied to vehicle {vehicle_number}.",
                f"Vehicle {vehicle_number} is set to full.",
                f"Vehicle {vehicle_number} has been updated to full.",
                f"Vehicle {vehicle_number} is now set to full."
            ]
    elif amount == 'stop':
        if vehicle_number == 'all':
            messages = [
                "All vehicles are now stopped.",
                "All your vehicles are set to stop.",
                "Stop amount applied to all vehicles.",
                "Every vehicle is now stopped.",
                "Stop amount set for all vehicles."
            ]
        else:
            messages = [
                f"Vehicle {vehicle_number} is now stopped.",
                f"Stop amount applied to vehicle {vehicle_number}.",
                f"Vehicle {vehicle_number} is set to stop.",
                f"Vehicle {vehicle_number} has been updated to stop.",
                f"Vehicle {vehicle_number} is now set to stop."
            ]
    else:
        if vehicle_number == 'all':
            messages = [
                f"All vehicles are now set to {amount}.",
                f"{amount} applied to all vehicles.",
                f"Every vehicle is now set to {amount}.",
                f"All vehicles have {amount} set.",
                f"{amount} set for all vehicles."
            ]
        else:
            messages = [
                f"Vehicle {vehicle_number} is now set to {amount}.",
                f"{amount} applied to vehicle {vehicle_number}.",
                f"Vehicle {vehicle_number} is set to {amount}.",
                f"Vehicle {vehicle_number} has been updated to {amount}.",
                f"Vehicle {vehicle_number} is now set to {amount}."
            ]

    return f"{random.choice(messages)} {get_call_end_message()}"


def get_success_message_1(amount: str, vehicle_number: str) -> str:
    amount = str(amount).lower()
    vehicle_number = str(vehicle_number).lower()
    if amount.lower() == 'full':
        if vehicle_number.lower() == 'all':
            messages = [
                "All vehicles have been successfully set to full. ",
                "The preset amount has been set to full for all your vehicles. ",
                "Every vehicle in your fleet is now updated to full. ",
                "Full amount has been applied to all your vehicles. ",
                "All vehicles now have a full preset amount. "
            ]
        else:
            messages = [
                f"Vehicle {vehicle_number} has been successfully set to full. ",
                f"The preset amount has been set to full for vehicle {vehicle_number}. ",
                f"Your vehicle {vehicle_number} is now updated to full. ",
                f"Full amount has been applied to your vehicle {vehicle_number}. ",
                f"Vehicle {vehicle_number} now has a full preset amount. "
            ]
    elif amount.lower() == 'stop':
        if vehicle_number.lower() == 'all':
            messages = [
                "All vehicles have been successfully set to stop. ",
                "The preset amount has been set to stop for all your vehicles. ",
                "Every vehicle in your fleet is now updated to stop. ",
                "Stop amount has been applied to all your vehicles. ",
                "All vehicles now have a stop preset amount. "
            ]
        else:
            messages = [
                f"Vehicle {vehicle_number} has been successfully set to stop. ",
                f"The preset amount has been set to stop for vehicle {vehicle_number}. ",
                f"Your vehicle {vehicle_number} is now updated to stop. ",
                f"Stop amount has been applied to your vehicle {vehicle_number}. ",
                f"Vehicle {vehicle_number} now has a stop preset amount. "
            ]
    else:
        if vehicle_number.lower() == 'all':
            messages = [
                f"All vehicles have been successfully set to {amount}. ",
                f"The preset amount of {amount} has been applied to all your vehicles. ",
                f"Every vehicle in your fleet is now updated to {amount}. ",
                f"{amount} has been applied to all your vehicles. ",
                f"All vehicles now have a preset amount of {amount}. "
            ]
        else:
            messages = [
                f"Vehicle {vehicle_number} has been successfully set to {amount}. ",
                f"The preset amount of {amount} has been applied to vehicle {vehicle_number}. ",
                f"Your vehicle {vehicle_number} is now updated to {amount}. ",
                f"{amount} has been applied to your vehicle {vehicle_number}. ",
                f"Vehicle {vehicle_number} now has a preset amount of {amount}. "
            ]

    return random.choice(messages) + get_call_end_message()


def get_call_end_message():
    messages = [
        "Thank you for using Ongo.",
        "We appreciate your trust in Ongo.",
        "Thanks for choosing Ongo!",
        "Thank you for your support.",
        "Thank you for using Ongo."
    ]
    endings = [
        "Goodbye, take care!",
        "Bye, take care!",
        "Have a great day!",
        "Talk to you soon!",
        "Bye now!"
    ]
    return f"{random.choice(messages)} {random.choice(endings)}"


def get_first_response():
    messages = [
        "How can I help you?",
        "Need help with your Ongo card?",
        "What can I do for you?",
        "How can I assist you?",
        "Need assistance?",
        "What do you need help with?"
    ]
    return random.choice(messages)


def get_first_response_v1():
    messages = [
        "How can I help you",
        "How can I help you with your Ongo prepaid card today?",
    ]
    return random.choice(messages)


def un_processable(title) -> None:
    """Speaker response for un-processable requests."""
    return f"I'm sorry {title}! I wasn't able to process your request."
