import os
from dotenv import load_dotenv
from flask import Flask, jsonify, request

import requests

from api.ongo.ongo_api import VehicleService
from modules.logger import custom_handler, logger
load_dotenv()

# Load environment variables
def load_environment():
    env = os.getenv('FLASK_ENV', 'development')
    if env == 'production':
        load_dotenv('.env.production')
    else:
        load_dotenv('.env.development')

load_environment()

# Initialize logger
handler = custom_handler(filename=os.path.join('logs', 'ongo_app_%d-%m-%Y_%H.log'))
logger.info("Switching to %s", handler.baseFilename)
logger.addHandler(hdlr=handler)

app = Flask(__name__)

vehicle_data_store = []

# Service setup
service = VehicleService(
    api_key=os.getenv('VEHICLE_API_KEY'),
    token=os.getenv('VEHICLE_TOKEN'),
    unique_id=os.getenv('VEHICLE_UNIQUE_ID'),
    base_url=os.getenv('VEHICLE_BASE_URL')
)


@app.route('/preset-vehicle', methods=['GET'])
def preset_vehicle():   
    vehicle_number = request.args.get('vehicle_number')
    vehicle_id = request.args.get('vehicle_id')
    preset_amount = request.args.get('preset_amount')
    mobile_number = request.args.get('mobile_number')
    user_id = request.args.get('user_id')

    logger.info(f"Received preset request: vehicle_number={vehicle_number}, vehicle_id={vehicle_id}, preset_amount={preset_amount}, mobile_number={mobile_number}, user_id={user_id}")

    # Validate vehicle number
    is_valid = False  # vehicle_number in vehicle_number_list
    try:
        vehicle_number = vehicle_number.replace(" ", "").strip().lower()
        if vehicle_number in "all":
            vehicle_number = "ALL"
        is_valid = True
    except ValueError:
        logger.error("Invalid vehicle number format", exc_info=True)
        is_valid = False

    status_code = 200
    amount_float = 0
    preset_quantity = "-1"
    # Validate preset amount
    try:
        preset_amount = preset_amount.replace(" ", "").strip().lower()
        if preset_amount in ("full", "stop"):
            if preset_amount == "full":
                preset_quantity = "FULL TANK"
                preset_amount = ''
            elif preset_amount == "stop":
                preset_quantity = "STOP FUELING"
                preset_amount = ''
        else:
            amount_float = float(preset_amount)
        is_valid_preset = True
    except ValueError:
        logger.error("Invalid preset amount format", exc_info=True)
        is_valid_preset = False

    # Determine message and save data if valid
    if is_valid and is_valid_preset:
        try:
            is_uat_bypass = False
            if is_uat_bypass:
                message_to_user = f"Vehicle {vehicle_number} has been successfully preset with an amount of {preset_amount}. Thank you for using Ongo Service"
            else:
                # vehicle_id = get_vehicle_id(vehicle_number)
                result = service.post_vehicle_data(
                    mobile_no=mobile_number,
                    preset_amount=preset_amount,
                    preset_qty=preset_quantity,
                    for_userid=user_id,
                    org_id=8,
                    vehicle_no=vehicle_number,
                    vehicle_id=vehicle_id
                )

                # Parsing the response
                status = result.get('Status')
                message = result.get('Message')

                if status == "00":
                    message_to_user = f"Successfully set the amount to {preset_amount} for vehicle {vehicle_number}. Thank you for using Ongo Service."
                    # message_to_user = f"{message} Thank you for using Ongo Service"
                    # Save the data
                    vehicle_data_store.append({
                        "vehicle_number": vehicle_number,
                        "amount": amount_float
                    })
                    status_code = 200
                    logger.info(f"Preset successful for vehicle_number={vehicle_number}, preset_amount={preset_amount}")
                elif status == "01":
                    message_to_user = f"error: {message}"
                    status_code = 400
                    logger.error(f"Preset failed for vehicle_number={vehicle_number}, preset_amount={preset_amount}. Status: {status}, Message: {message}")
                else:
                    message_to_user = "Please try again later. Thank you for using Ongo Service"
                    status_code = 200
                    logger.error(f"Preset failed for vehicle_number={vehicle_number}, preset_amount={preset_amount}. Status: {status}, Message: {message}")

            # message_to_user = f"Vehicle {vehicle_number} has been successfully preset with an amount of {amount}. Thank you for using Ongo Service"
        except requests.RequestException as e:            
            message_to_user = "error: Please try again later. Thank you for using Ongo Service"
            status_code = 400
            logger.error(f"An error occurred during the preset request: {e}", exc_info=True)

    elif not is_valid:
        message_to_user = f"error: Vehicle number {vehicle_number} is not valid."
        status_code = 400
        logger.error(f"Validation failed for vehicle_number={vehicle_number}.")
    else:
        message_to_user = f"error: Preset amount '{preset_amount}' is not valid. Please enter a valid numerical value."
        status_code = 400
        logger.error(f"Validation failed for preset_amount={preset_amount}")

    return jsonify({
        # "vehicle_number_valid": is_valid,
        # "preset_amount_valid": is_valid_preset,
        "message_to_user": message_to_user
    }), status_code


if __name__ == '__main__':
    app.run(port=5001, host='0.0.0.0')
