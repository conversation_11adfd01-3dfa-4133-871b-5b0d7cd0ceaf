import aiohttp
import asyncio
import time
from typing import Union

import requests
import json


class ONGOData:
    def __init__(self, accuracy, answer, query, response_time, source):
        self.accuracy = accuracy
        self.answer: str = answer
        self.query = query
        self.response_time = response_time
        self.source = source

    def is_source_ongo_faqs(self, match: str) -> bool:
        return match in self.source  # "./data/ongo_faqs.csv"

    @classmethod
    def from_dict(cls, data):
        return cls(
            accuracy=data["accuracy"],
            answer=data["answer"],
            query=data["query"],
            response_time=data["response_time"],
            source=data["sources"]["source"]
        )

    def parse_response(self) -> str:
        """
        Parse and format the instance for readability.

        :return: A formatted string representation of the instance.
        """
        formatted_response = (
            f"Query: {self.query}\n"
            f"Answer: {self.answer}\n"
            f"Response Time: {self.response_time} seconds\n"
            f"Sources: Row {self.source}"
        )
        return formatted_response


class OngoFaqsService:
    def __init__(self):
        """
        Initialize the OngoFaqsService with the base URL for the API.

        """
        self.base_url = "http://localhost:3020"

    async def get_best_match(self, query) -> Union[ONGOData, None]:
        url = f"{self.base_url}/best_match"
        payload = self._create_payload(query)
        headers = self._create_headers()

        # print(f"Sending request to {url}")
        # print(f"Payload: {payload}")
        # print(f"Headers: {headers}")

        async with aiohttp.ClientSession() as session:
            try:
                start_time = time.time()
                async with session.post(url, json=payload, headers=headers) as response:
                    response.raise_for_status()  # Raise an error for bad status codes
                    end_time = time.time()

                    response_time = end_time - start_time
                    print(f"Response time for get_best_match: {response_time:.2f} seconds")

                    response_data = await response.json()
                    return ONGOData.from_dict(response_data)
            except (aiohttp.ClientError, aiohttp.ContentTypeError, KeyError) as e:
                print(f"Error during request: {e}")
                if response is not None:
                    print(f"Response status: {response.status}")
                    try:
                        error_detail = await response.text()
                        print(f"Error detail: {error_detail}")
                    except Exception as detail_error:
                        print(f"Error retrieving error details: {detail_error}")
                return None

    @staticmethod
    def _create_payload(query):
        """
        Create the JSON payload for the request.

        :param query: The query string to include in the payload.
        :return: The JSON payload as a string.
        """
        return {"query": query}

    @staticmethod
    def _create_headers():
        """
        Create the headers for the request.

        :return: A dictionary containing the headers.
        """
        return {'Content-Type': 'application/json'}


# Example usage
if __name__ == "__main__":
    async def main():
        service = OngoFaqsService()
        response = await service.get_best_match("How can I replace my lost ONGO card?")
        if response:
            formatted_response = response.parse_response()
            print(formatted_response)
        else:
            print("No response received.")


    asyncio.run(main())
