import requests
import json


class OngoFaqsService:
    def __init__(self):
        """
        Initialize the OngoFaqsService with the base URL for the API.

        """
        self.base_url = "http://localhost:3020"

    def get_best_match(self, query):
        """
        Send a POST request to the best_match endpoint with the provided query.

        :param query: The query string to send to the API.
        :return: The response text from the API.
        """
        url = f"{self.base_url}/best_match"
        payload = self._create_payload(query)
        headers = self._create_headers()

        response = requests.post(url, headers=headers, data=payload)
        return response.json()

    @staticmethod
    def _create_payload(query):
        """
        Create the JSON payload for the request.

        :param query: The query string to include in the payload.
        :return: The JSON payload as a string.
        """
        return json.dumps({"query": query})

    @staticmethod
    def _create_headers():
        """
        Create the headers for the request.

        :return: A dictionary containing the headers.
        """
        return {'Content-Type': 'application/json'}

    @staticmethod
    def parse_response(response):
        """
        Parse and format the API response for readability.

        :param response: The API response as a dictionary.
        :return: A formatted string representation of the response.
        """
        answer = response.get("answer", "No answer provided")
        query = response.get("query", "No query provided")
        response_time = response.get("response_time", "No response time provided")
        sources = response.get("sources", {})

        formatted_response = (
            f"Query: {query}\n"
            f"Answer: {answer}\n"
            f"Response Time: {response_time} seconds\n"
            f"Sources: Row {sources.get('row', 'N/A')}, Source {sources.get('source', 'N/A')}"
        )
        return formatted_response


# Example usage
if __name__ == "__main__":
    service = OngoFaqsService()
    response = service.get_best_match("How can I replace my lost ONGO card?")
    formatted_response = service.parse_response(response)
    print(formatted_response)
