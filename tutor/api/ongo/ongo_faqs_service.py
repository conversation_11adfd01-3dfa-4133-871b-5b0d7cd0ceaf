import time
import json
import httpx
import asyncio
from typing import Union


class ONGOData:
    def __init__(self, accuracy, answer, query, response_time, source):
        self.accuracy = accuracy
        self.answer: str = answer
        self.query = query
        self.response_time = response_time
        self.source = source

    def is_source_ongo_faqs(self, match: str) -> bool:
        return match in self.source  # "./data/ongo_faqs.csv"

    @classmethod
    def from_dict(cls, data):
        return cls(
            accuracy=data["accuracy"],
            answer=data["answer"],
            query=data["query"],
            response_time=data["response_time"],
            source=data["sources"]["source"]
        )

    def parse_response(self):
        """
        Parse and format the instance for readability.

        :return: A formatted string representation of the instance.
        """
        formatted_response = (
            f"Query: {self.query}\n"
            f"Answer: {self.answer}\n"
            f"Response Time: {self.response_time} seconds\n"
            f"Sources: Row {self.source}"
        )
        return formatted_response


class OngoFaqsService:
    def __init__(self, logger):
        """
        Initialize the OngoFaqsService with the base URL for the API.

        """
        self.base_url = "http://localhost:3020"
        self.client = httpx.AsyncClient()
        self.logger = logger

    async def get_best_match(self, query) -> Union[ONGOData, None]:
        """
        Send a POST request to the best_match endpoint with the provided query.

        :param query: The query string to send to the API.
        :return: The response text from the API.
        """
        url = f"{self.base_url}/best_match"
        payload = self._create_payload(query)
        headers = self._create_headers()

        try:
            # start_time = time.time()
            response = await self.client.post(url, headers=headers, json=payload)
            response.raise_for_status()  # Raise an error for bad status codes
            # end_time = time.time()

            # response_time = end_time - start_time
            # print(f"Response time for get_best_match: {response_time:.2f} seconds")

            response_data = response.json()
            return ONGOData.from_dict(response_data)
        except (httpx.RequestError, json.JSONDecodeError, KeyError) as e:
            self.logger(f"Error during request: {e}")
            return None

    @staticmethod
    def _create_payload(query):
        """
        Create the JSON payload for the request.

        :param query: The query string to include in the payload.
        :return: The JSON payload as a string.
        """
        return {"query": query}

    @staticmethod
    def _create_headers():
        """
        Create the headers for the request.

        :return: A dictionary containing the headers.
        """
        return {'Content-Type': 'application/json'}


# Example usage
if __name__ == "__main__":
    async def main():
        service = OngoFaqsService()
        query = "What is the capital of France?"
        result = await service.get_best_match(query)
        if result:
            print(result.parse_response())


    # Run the async main function
    asyncio.run(main())
