# _init_.py

import os
from multiprocessing import current_process

version = "4.4.2"

install_script = os.path.join(os.path.dirname(__file__), 'lib', 'install.sh')

try:
    if current_process().name == 'MainProcess':
        current_process().name = os.environ.get('PROCESS_NAME', 'AITUTOR')

    # from .main import start  # noqa: F401
except ImportError as error:
    try:
        print(f"Please run\n\n{install_script}")
    except NameError:
        pass
    raise UserWarning(f"{error.__str__()}\n\nPlease run\n\n{install_script}\n\n"
                      "Note: Shell script will quit for any non-zero exit status, "
                      "so it might have to be triggered twice.")
