from flask import Flask, render_template
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import asyncio
import websockets
import json
import threading
import logging


app = Flask(__name__)
socketio = SocketIO(app, async_mode='threading', cors_allowed_origins="*")

# List to store active users
active_users = []

async def fetch_active_users():
    while True:
        try:
            async with websockets.connect('ws://localhost:3010') as websocket:
                # Emit server_status as 'up' when connection is established
                socketio.emit('server_status', {'status': 'up'}, namespace='/')
                logging.info("Emitting server_status: up due to successful connection")

                await websocket.send(json.dumps({"type": "request_active_users", "session": "webapplication"}))
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(response)
                    if data["type"] == "active_users":
                        global active_users
                        active_users = data["data"]
                        socketio.emit('update_users', active_users, namespace='/')
                except asyncio.TimeoutError:
                    logging.error("WebSocket recv() timed out")
                    socketio.emit('server_status', {'status': 'down'}, namespace='/')
                    logging.info("Emitting server_status: down due to TimeoutError")
                await asyncio.sleep(5)
        except websockets.ConnectionClosedError:
            logging.error("WebSocket connection closed, reconnecting...")
            socketio.emit('server_status', {'status': 'down'}, namespace='/')
            logging.info("Emitting server_status: down due to ConnectionClosedError")
            await asyncio.sleep(5)
        except Exception as e:
            logging.error(f"Unexpected error: {e}")
            socketio.emit('server_status', {'status': 'down'}, namespace='/')
            logging.info("Emitting server_status: down due to Exception")
            await asyncio.sleep(5)

@app.route('/')
def index():
    return render_template('index.html')

@socketio.on('connect', namespace='/')
def handle_connect():
    emit('update_users', active_users, namespace='/')
    emit('server_status', {'status': 'test_emit'}, namespace='/')
    logging.info("Emitting test server_status: test_emit")

def start_background_loop(loop):
    asyncio.set_event_loop(loop)
    loop.run_forever()

if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG)

    loop = asyncio.new_event_loop()
    t = threading.Thread(target=start_background_loop, args=(loop,))
    t.start()

    asyncio.run_coroutine_threadsafe(fetch_active_users(), loop)

    socketio.run(app, host='0.0.0.0', port=5000)
