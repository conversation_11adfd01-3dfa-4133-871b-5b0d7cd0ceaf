from flask import Flask, request
from twilio.twiml.voice_response import VoiceResponse
# import ai_logic
# import session_storage
import random

def process_user_input(user_input):
    """Process the user's input and return an appropriate response."""
    user_input = user_input.lower()

    if 'hello' in user_input:
        return "Hello! How can I assist you today?"
    elif 'order' in user_input:
        return "You can place an order for food, groceries, or other items. What would you like to order?"
    elif 'appointment' in user_input:
        return "I can help you schedule an appointment. Please provide the date and time."
    elif 'help' in user_input:
        return "I'm here to help! Please tell me what assistance you need."
    else:
        return "I'm sorry, I didn't quite understand that. Could you please repeat?"

def get_follow_up_question():
    """Return a follow-up question to keep the conversation going."""
    questions = [
        "Is there anything else you would like to ask?",
        "How can I assist you further?",
        "What else would you like to know?",
        "Do you need help with anything else?"
    ]
    return random.choice(questions)


class SessionStorage:
    """Simple in-memory session storage for managing call sessions."""
    
    def __init__(self):
        self.sessions = {}
    
    def create_session(self, call_sid, phone_number):
        """Create a session for the user."""
        self.sessions[call_sid] = {
            'phone_number': phone_number,
            'conversation_history': []
        }
    
    def get_session(self, call_sid):
        """Retrieve session information by CallSid."""
        return self.sessions.get(call_sid)
    
    def update_session(self, call_sid, key, value):
        """Update session with new information."""
        if call_sid in self.sessions:
            self.sessions[call_sid][key] = value

app = Flask(__name__)
app.secret_key = 'your_secret_key'  # Required for session management
session_storage = SessionStorage()

# Route to handle incoming voice calls and start the conversation
@app.route("/voice", methods=["POST"])
def voice():
    """Respond to incoming calls and gather user input for continuous conversation."""
    resp = VoiceResponse()

    # Unique CallSid for tracking individual calls
    call_sid = request.form.get('CallSid')
    caller_number = request.form.get('From')

    # Check if it's a returning caller, continue the conversation
    if session_storage.get_session(call_sid):
        resp.say("Welcome back! How can I assist you today?")
    else:
        # Create a session for the caller
        session_storage.create_session(call_sid, caller_number)
        resp.say("Hello! You are speaking with an AI assistant. Let's start. How can I help you today?")

    # Gather speech input for continuous interaction
    gather = resp.gather(
        input='speech',
        action='/process_speech',
        method='POST',
        language='en-US',
        timeout=5
    )

    # If no speech is detected, redirect to voice route again
    resp.redirect('/voice')

    return str(resp)

# Route to process speech input and continue the conversation
@app.route("/process_speech", methods=["POST"])
def process_speech():
    """Process the user's speech input and continue the conversation."""
    resp = VoiceResponse()

    # Retrieve the speech result and session info
    user_input = request.form.get('SpeechResult')
    call_sid = request.form.get('CallSid')

    # If user input exists, process it through AI logic
    if user_input:
        session_info = session_storage.get_session(call_sid)
        if not session_info:
            resp.say("I seem to have lost your session. Let's start over.")
            resp.redirect('/voice')
            return str(resp)

        # Process the user's input and generate an AI response
        response_text = process_user_input(user_input)

        # Speak the AI response to the user
        resp.say(response_text)

        # Ask a follow-up question to continue the conversation
        follow_up_question = get_follow_up_question()
        resp.say(follow_up_question)

        # Continue listening for further input
        gather = resp.gather(
            input='speech',
            action='/process_speech',
            method='POST',
            language='en-US',
            timeout=5
        )
    else:
        # If no speech result, ask the user to try again
        resp.say("I didn't hear anything. Can you please say something?")
        resp.redirect('/voice')

    return str(resp)


class SessionStorage:
    """Simple in-memory session storage for managing call sessions."""
    
    def __init__(self):
        self.sessions = {}
    
    def create_session(self, call_sid, phone_number):
        """Create a session for the user."""
        self.sessions[call_sid] = {
            'phone_number': phone_number,
            'conversation_history': []
        }
    
    def get_session(self, call_sid):
        """Retrieve session information by CallSid."""
        return self.sessions.get(call_sid)
    
    def update_session(self, call_sid, key, value):
        """Update session with new information."""
        if call_sid in self.sessions:
            self.sessions[call_sid][key] = value

if __name__ == "__main__":
    app.run(port=5000, host='0.0.0.0')
