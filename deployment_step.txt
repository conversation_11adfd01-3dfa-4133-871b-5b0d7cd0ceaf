sudo apt-get update
sudo apt upgrade -y
sudo apt install python3-pip python3-dev
sudo apt install python3-virtualenv
sudo apt-get install -y portaudio19-dev
udo apt-get install -y unixodbc-dev

# sudo pip3 install virtualenv

mkdir ~/ai_voice_mate

cd ~/ai_voice_mate
# virtualenv .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt # pip install torch~=2.3.1 --no-cache-dir             

python3 main.py
or
.venv/bin/python main.py

.venv/bin/python tutor/twilio_app.py

python3 voice_call_app.py
lsof -ti:5001 | xargs kill -9 2>/dev/null || echo "No process found on port 5001"
-------------
Use tmux:
You can use terminal multiplexer programs like tmux or screen to run your process in a session that you can detach from and reattach later.

With tmux:
Start a new tmux session:

bash
Copy code
tmux new -s mysession_mate
Run your Python script:

bash
Copy code
python main.py
Detach from the session: Press Ctrl + B, then D.

The process will continue running in the background. To reattach to the session later, run:

bash
Copy code
tmux attach -t mysession_mate
----------
Setting Up Gunicorn
Create a Gunicorn systemd service file.

bash
Copy code
sudo nano /etc/systemd/system/ai_voice_bot_websocket_v5.service
or 
sudo vim /etc/systemd/system/ai_voice_bot_websocket_v5.service

Add the following content to the file:

ini
Copy code
[Unit]
Description=AI voice bot websocket server v5
After=network.target

[Service]
User=ubuntu
Group=www-data
WorkingDirectory=/home/<USER>/ai_voice_bot_v5
Environment="PATH=/home/<USER>/ai_voice_bot_v5/ai_voice_bot_v5_env/bin:/usr/bin:/usr/local/bin"
ExecStart=/home/<USER>/ai_voice_bot_v5/ai_voice_bot_v5_env/bin/python /home/<USER>/ai_voice_bot_v5/websocket_server.py
Restart=always

[Install]
WantedBy=multi-user.target

# Start and Enable the Gunicorn Service

sudo systemctl start ai_voice_bot_websocket_v5
sudo systemctl enable ai_voice_bot_websocket_v5

# Update and Install Necessary Packages
sudo apt install nginx git -y

# Configure Nginx
# Create a new server block configuration file:
bash
Copy code
sudo nano /etc/nginx/sites-available/ai_voice_bot_websocket_v5
Or
sudo vim /etc/nginx/sites-available/ai_voice_bot_websocket_v5

server {
    listen 445 ssl;    
    server_name evosysai.com www.evosysai.com;

    ssl_certificate /etc/nginx/ssl/evosysai_com.crt;
    ssl_certificate_key /etc/nginx/ssl/evosysai_com.key;
    ssl_trusted_certificate /etc/nginx/ssl/evosysai_com.ca-bundle;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';

    location / {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # WebSocket headers
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        proxy_pass http://127.0.0.1:3010;
        proxy_http_version 1.1;
        proxy_read_timeout 3600s;
    }
}


# Restart Gunicorn and Nginx

sudo systemctl daemon-reload
sudo systemctl restart ai_voice_bot_websocket_v5
sudo systemctl restart nginx

# check servie run or not
ps ax | grep ai_voice_bot_websocket_v5
sudo ss -tulnp | grep 3010
sudo systemctl status ai_voice_bot_websocket_v5

--------------------------------

ongo_app_v5

----------
Setting Up Gunicorn
Create a Gunicorn systemd service file.

bash
Copy code
sudo nano /etc/systemd/system/ongo_app_v5.service
or 
sudo vim /etc/systemd/system/ongo_app_v5.service

Add the following content to the file:

ini
Copy code
[Unit]
Description=Gunicorn instance to serve ongo_app_v5 server to server communition this service connected with ags ongo production. 
After=network.target

[Service]
User=ubuntu
Group=www-data
WorkingDirectory=/home/<USER>/ai_voice_bot_v5
Environment="PATH=/home/<USER>/ai_voice_bot_v5/ai_voice_bot_v5_env/bin"
ExecStart=/home/<USER>/ai_voice_bot_v5/ai_voice_bot_v5_env/bin/gunicorn --workers 4 --threads 2 --bind 0.0.0.0:5001 wsgi:app

[Install]
WantedBy=multi-user.target

# Start and Enable the Gunicorn Service

sudo systemctl start ongo_app_v5
sudo systemctl enable ongo_app_v5

# Update and Install Necessary Packages
sudo apt install nginx git -y

# Configure Nginx
# Create a new server block configuration file:
bash
Copy code
sudo nano /etc/nginx/sites-available/ongo_app_v5
Or
sudo vim /etc/nginx/sites-available/ongo_app_v5

server {
    listen 80;
    server_name ongo_app_v5;

    location / {
        include proxy_params;
        proxy_pass http://127.0.0.1:5001;
    }
}


# Restart Gunicorn and Nginx

sudo systemctl daemon-reload
sudo systemctl restart ongo_app_v5
sudo systemctl restart nginx

# check servie run or not
ps ax | grep ongo_app_v5
sudo ss -tulnp | grep 5001
sudo systemctl status ongo_app_v5


test python tutor/api/faqs_service.py